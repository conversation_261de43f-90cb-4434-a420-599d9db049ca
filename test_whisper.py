import os
import sys
import whisper
import tempfile

def main():
    print("Python version:", sys.version)
    print("Whisper version:", whisper.__version__)
    
    # 加载模型
    print("正在加载模型...")
    model = whisper.load_model("base")
    print("模型加载完成")
    
    # 创建临时音频文件用于测试
    if os.path.exists("temp_video.mp4"):
        print("使用现有的temp_video.mp4进行测试")
        # 转录音频
        print("开始转录...")
        result = model.transcribe("temp_video.mp4")
        print("转录完成")
        print("转录结果:", result["text"])
    else:
        print("未找到测试音频文件temp_video.mp4")

if __name__ == "__main__":
    main()
