import sys
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path}")

try:
    import jax
    print(f"JAX version: {jax.__version__}")
except ImportError as e:
    print(f"Error importing JAX: {e}")

try:
    import numpy
    print(f"NumPy version: {numpy.__version__}")
except ImportError as e:
    print(f"Error importing NumPy: {e}")

try:
    import whisper_jax
    print(f"whisper_jax version: {whisper_jax.__version__}")
except ImportError as e:
    print(f"Error importing whisper_jax: {e}")
except Exception as e:
    print(f"Error with whisper_jax: {e}")
