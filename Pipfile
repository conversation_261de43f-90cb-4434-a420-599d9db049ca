[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
absl-py = "==2.3.0"
aiofiles = "==24.1.0"
aiohappyeyeballs = "==2.6.1"
aiohttp = "==3.12.7"
aiosignal = "==1.3.2"
annotated-types = "==0.7.0"
anyio = "==4.9.0"
attrs = "==25.3.0"
cached-property = "==2.0.1"
certifi = "==2025.4.26"
charset-normalizer = "==3.4.2"
chex = "==0.1.89"
click = "==8.2.1"
datasets = "==3.6.0"
dill = "==0.3.8"
etils = "==1.12.2"
fastapi = "==0.115.12"
ffmpy = "==0.6.0"
filelock = "==3.18.0"
flax = "==0.10.6"
frozenlist = "==1.6.2"
fsspec = "==2025.3.0"
gradio = "==5.32.1"
gradio-client = "==1.10.2"
groovy = "==0.1.2"
h11 = "==0.16.0"
hf-xet = "==1.1.3"
httpcore = "==1.0.9"
httpx = "==0.28.1"
huggingface-hub = "==0.32.4"
humanize = "==4.12.3"
idna = "==3.10"
importlib-resources = "==6.5.2"
jax = "==0.6.1"
jaxlib = "==0.6.1"
jinja2 = "==3.1.6"
llvmlite = "==0.44.0"
markdown-it-py = "==3.0.0"
markupsafe = "==3.0.2"
mdurl = "==0.1.2"
ml-dtypes = "==0.5.1"
more-itertools = "==10.7.0"
mpmath = "==1.3.0"
msgpack = "==1.1.0"
multidict = "==6.4.4"
multiprocess = "==0.70.16"
nest-asyncio = "==1.6.0"
networkx = "*"
numba = "==0.61.2"
numpy = "==2.2.6"
nvidia-cublas-cu12 = "==********"
nvidia-cuda-cupti-cu12 = "==12.6.80"
nvidia-cuda-nvrtc-cu12 = "==12.6.77"
nvidia-cuda-runtime-cu12 = "==12.6.77"
nvidia-cudnn-cu12 = "==********"
nvidia-cufft-cu12 = "==********"
nvidia-cufile-cu12 = "==********"
nvidia-curand-cu12 = "==*********"
nvidia-cusolver-cu12 = "==********"
nvidia-cusparse-cu12 = "==********"
nvidia-cusparselt-cu12 = "==0.6.3"
nvidia-nccl-cu12 = "==2.26.2"
nvidia-nvjitlink-cu12 = "==12.6.85"
nvidia-nvtx-cu12 = "==12.6.77"
openai-whisper = "==20240930"
opt-einsum = "==3.4.0"
optax = "==0.2.4"
orbax-checkpoint = "==0.11.13"
orjson = "==3.10.18"
packaging = "==25.0"
pandas = "==2.2.3"
pillow = "==11.2.1"
propcache = "==0.3.1"
protobuf = "==6.31.1"
psutil = "==7.0.0"
pyarrow = "==20.0.0"
pydantic = "==2.11.5"
pydantic-core = "==2.33.2"
pydub = "==0.25.1"
pygments = "==2.19.1"
python-dateutil = "==2.9.0.post0"
python-magic = "==0.4.27"
python-multipart = "==0.0.20"
pytz = "==2025.2"
pyyaml = "==6.0.2"
regex = "==2024.11.6"
requests = "==2.32.3"
rich = "==14.0.0"
ruff = "==0.11.12"
safehttpx = "==0.1.6"
safetensors = "==0.5.3"
scipy = "==1.15.3"
semantic-version = "==2.10.0"
shellingham = "==1.5.4"
simplejson = "==3.20.1"
six = "==1.17.0"
sniffio = "==1.3.1"
starlette = "==0.46.2"
sympy = "==1.14.0"
tensorstore = "==0.1.75"
tiktoken = "==0.9.0"
tokenizers = "==0.21.1"
tomlkit = "==0.13.2"
toolz = "==1.0.0"
torch = "==2.7.0"
tqdm = "==4.67.1"
transformers = "==4.52.4"
treescope = "==0.1.9"
triton = "==3.3.0"
typer = "==0.16.0"
typing-inspection = "==0.4.1"
typing-extensions = "==4.14.0"
tzdata = "==2025.2"
urllib3 = "==2.4.0"
uvicorn = "==0.34.3"
websockets = "==15.0.1"
xxhash = "==3.5.0"
yarl = "==1.20.0"
yt-dlp = "==2025.5.22"
zipp = "==3.22.0"

[dev-packages]

[requires]
python_version = "3.10"
