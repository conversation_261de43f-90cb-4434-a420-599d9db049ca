#!/bin/bash

# ===================================
# Whisper-JAX 服务管理脚本使用说明
# ===================================
#
# 【常用操作指令】
# 启动服务:    ./start_whisper_jax.sh start
# 停止服务:    ./start_whisper_jax.sh stop
# 重启服务:    ./start_whisper_jax.sh restart
# 查看状态:    ./start_whisper_jax.sh status
#
# 【日志和监控】
# 日志文件位置: /home/<USER>/whisper-jax/app/app.log
# 查看日志:     tail -f /home/<USER>/whisper-jax/app/app.log
# 查看进程:     ps aux | grep app.py
#
# 【常见问题解决】
# 1. 如果服务无法正常停止，脚本会自动强制终止进程
# 2. 启动失败时，请检查日志文件获取详细错误信息
# 3. 服务默认在端口7850上运行，可通过 http://localhost:7850 访问
# 4. 如果端口被占用，脚本会尝试释放端口并重新启动服务
#
# 【环境配置】
# 服务目录:     /home/<USER>/whisper-jax
# 虚拟环境:     /home/<USER>/whisper-jax/venv
# PID文件:     /home/<USER>/whisper-jax/app.pid
# 
# 【调试模式】
# 设置环境变量启用调试模式: DEBUG=true ./start_whisper_jax.sh start
#
# 配置变量
APP_DIR="/home/<USER>/whisper-jax"
# VENV_DIR="$APP_DIR/venv"  # Pipenv manages virtual environments, this line is no longer needed.
APP_PATH="$APP_DIR/app/app.py"
LOG_FILE="$APP_DIR/app/app.log"  # 修正日志文件路径
PID_FILE="$APP_DIR/app.pid"

# 检查是否已经在运行
check_if_running() {
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 停止服务
stop_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "正在停止服务 (PID: $PID)..."
            kill "$PID"
            # 等待进程结束，最多等待5秒
            for i in {1..10}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    break
                fi
                sleep 0.5
            done
            # 如果进程还在运行，强制终止
            if ps -p "$PID" > /dev/null 2>&1; then
                echo "服务未能正常停止，正在强制终止..."
                kill -9 "$PID"
                sleep 1
            fi
        fi
        rm -f "$PID_FILE"
    fi

    # 确保没有遗留的 Python 进程
    pkill -f "python.*app.py" || true
    sleep 1
    # 确保端口已释放
    fuser -k 7850/tcp || true
    sleep 1
    echo "服务已停止"
}

# 启动服务
start_service() {
    if check_if_running; then
        echo "服务已经在运行中 (PID: $(cat "$PID_FILE"))"
        exit 1
    fi

    # 导航到项目目录
    cd "$APP_DIR" || exit 1

    # 检查虚拟环境 (Pipenv handles this automatically)
    # if [ ! -f "$VENV_DIR/bin/activate" ]; then
    #     echo "错误：找不到虚拟环境，请确保已正确安装"
    #     exit 1
    # fi

    # 激活虚拟环境 (Pipenv handles this via 'pipenv run')
    # source "$VENV_DIR/bin/activate"

    # 设置环境变量
    export PYTHONPATH="$APP_DIR"

    # 清除之前的日志
    [ -f "$LOG_FILE" ] && mv "$LOG_FILE" "${LOG_FILE}.old"

    echo "正在启动服务..."
    if [ "$DEBUG" = "true" ]; then
        pipenv run python "$APP_PATH" 2>&1 | tee "$LOG_FILE" &
    else
        nohup pipenv run python "$APP_PATH" > "$LOG_FILE" 2>&1 &
    fi
    
    # 保存 PID
    echo $! > "$PID_FILE"
    
    # 等待服务启动
    echo "等待服务启动..."
    for i in {1..10}; do
        if curl -s http://localhost:7850/ > /dev/null 2>&1; then
            echo "服务已成功启动 (PID: $(cat "$PID_FILE"))"
            echo "日志文件: $LOG_FILE"
            exit 0
        fi
        if ! ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
            echo "服务进程已退出，启动失败"
            cat "$LOG_FILE"
            exit 1
        fi
        sleep 1
    done

    # 如果超时但进程还在运行，就认为是正常的
    if ps -p "$(cat "$PID_FILE")" > /dev/null 2>&1; then
        echo "服务正在运行 (PID: $(cat "$PID_FILE"))"
        echo "日志文件: $LOG_FILE"
        exit 0
    fi

    echo "服务启动超时，请检查日志文件: $LOG_FILE"
    stop_service
    exit 1
}

# 根据命令行参数执行相应操作
case "${1:-start}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        stop_service
        sleep 2
        start_service
        ;;
    status)
        if check_if_running; then
            echo "服务正在运行 (PID: $(cat "$PID_FILE"))"
        else
            echo "服务未运行"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
