import sys
print(f"Python version: {sys.version}")

try:
    import fix_jax_extend
except Exception as e:
    print(f"Error importing fix_jax_extend: {e}")

try:
    import jax
    print(f"JAX version: {jax.__version__}")
    print(f"jax.extend exists: {hasattr(jax, 'extend')}")
except ImportError as e:
    print(f"Error importing JAX: {e}")

try:
    import whisper_jax
    print(f"whisper_jax version: {whisper_jax.__version__}")
except ImportError as e:
    print(f"Error importing whisper_jax: {e}")
except Exception as e:
    print(f"Error with whisper_jax: {e}")
