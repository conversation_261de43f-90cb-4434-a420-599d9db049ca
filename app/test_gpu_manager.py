#!/usr/bin/env python3
"""
GPU管理器测试脚本
用于测试GPU分配和释放是否正常工作
"""

import threading
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟GPU管理器配置
GPUS = ["0", "1"]

class GPUManager:
    def __init__(self):
        self.gpu_states = {gpu: {"in_use": False, "last_used": 0} for gpu in GPUS}
        self.lock = threading.Lock()

    def acquire_gpu(self):
        with self.lock:
            # 按优先级顺序查找可用的GPU
            for gpu in GPUS:
                if not self.gpu_states[gpu]["in_use"]:
                    self.gpu_states[gpu]["in_use"] = True
                    self.gpu_states[gpu]["last_used"] = time.time()
                    print(f"GPU {gpu} 已被分配")
                    return gpu
            print("所有GPU都在使用中")
            return None

    def release_gpu(self, gpu):
        with self.lock:
            if gpu in self.gpu_states:
                self.gpu_states[gpu]["in_use"] = False
                print(f"GPU {gpu} 已被释放")

    def get_gpu_status(self):
        """获取GPU使用状态"""
        with self.lock:
            return {gpu: state["in_use"] for gpu, state in self.gpu_states.items()}

def test_gpu_allocation():
    """
    测试GPU分配功能
    """
    print("测试GPU分配功能...")
    print(f"可用GPU: {GPUS}")
    
    # 创建GPU管理器
    gpu_manager = GPUManager()
    
    # 测试单个GPU分配
    print("\n1. 测试单个GPU分配:")
    gpu1 = gpu_manager.acquire_gpu()
    print(f"   分配到GPU: {gpu1}")
    print(f"   GPU状态: {gpu_manager.get_gpu_status()}")
    
    # 测试第二个GPU分配
    print("\n2. 测试第二个GPU分配:")
    gpu2 = gpu_manager.acquire_gpu()
    print(f"   分配到GPU: {gpu2}")
    print(f"   GPU状态: {gpu_manager.get_gpu_status()}")
    
    # 测试第三个GPU分配（应该失败）
    print("\n3. 测试第三个GPU分配（应该失败）:")
    gpu3 = gpu_manager.acquire_gpu()
    print(f"   分配到GPU: {gpu3}")
    print(f"   GPU状态: {gpu_manager.get_gpu_status()}")
    
    # 释放第一个GPU
    print("\n4. 释放第一个GPU:")
    if gpu1:
        gpu_manager.release_gpu(gpu1)
        print(f"   已释放GPU: {gpu1}")
        print(f"   GPU状态: {gpu_manager.get_gpu_status()}")
    
    # 再次尝试分配GPU
    print("\n5. 再次尝试分配GPU:")
    gpu4 = gpu_manager.acquire_gpu()
    print(f"   分配到GPU: {gpu4}")
    print(f"   GPU状态: {gpu_manager.get_gpu_status()}")
    
    # 清理
    if gpu2:
        gpu_manager.release_gpu(gpu2)
    if gpu4:
        gpu_manager.release_gpu(gpu4)
    
    print(f"\n最终GPU状态: {gpu_manager.get_gpu_status()}")

def test_concurrent_allocation():
    """
    测试并发GPU分配
    """
    print("\n" + "="*50)
    print("测试并发GPU分配...")
    
    gpu_manager = GPUManager()
    results = []
    
    def worker(worker_id):
        """工作线程函数"""
        print(f"工作线程 {worker_id} 开始")
        
        # 尝试获取GPU
        gpu_id = gpu_manager.acquire_gpu()
        if gpu_id:
            print(f"工作线程 {worker_id} 获取到GPU {gpu_id}")
            results.append(f"线程{worker_id}:GPU{gpu_id}")
            
            # 模拟工作
            time.sleep(2)
            
            # 释放GPU
            gpu_manager.release_gpu(gpu_id)
            print(f"工作线程 {worker_id} 释放了GPU {gpu_id}")
        else:
            print(f"工作线程 {worker_id} 未能获取到GPU")
            results.append(f"线程{worker_id}:无GPU")
    
    # 创建多个线程
    threads = []
    for i in range(4):  # 创建4个线程，但只有2个GPU
        thread = threading.Thread(target=worker, args=(i+1,))
        threads.append(thread)
    
    # 启动所有线程
    print("启动4个工作线程...")
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"\n并发测试结果: {results}")
    print(f"最终GPU状态: {gpu_manager.get_gpu_status()}")

def test_stress_allocation():
    """
    压力测试GPU分配
    """
    print("\n" + "="*50)
    print("GPU分配压力测试...")
    
    gpu_manager = GPUManager()
    success_count = 0
    fail_count = 0
    
    def stress_worker(worker_id):
        nonlocal success_count, fail_count
        
        for i in range(10):  # 每个线程尝试10次
            gpu_id = gpu_manager.acquire_gpu()
            if gpu_id:
                success_count += 1
                time.sleep(0.1)  # 短暂持有GPU
                gpu_manager.release_gpu(gpu_id)
            else:
                fail_count += 1
            time.sleep(0.05)  # 短暂休息
    
    # 创建多个压力测试线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=stress_worker, args=(i+1,))
        threads.append(thread)
    
    start_time = time.time()
    
    # 启动所有线程
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    print(f"压力测试完成，用时: {end_time - start_time:.2f}秒")
    print(f"成功分配次数: {success_count}")
    print(f"分配失败次数: {fail_count}")
    print(f"最终GPU状态: {gpu_manager.get_gpu_status()}")

def main():
    print("GPU管理器测试工具")
    print("="*50)
    
    try:
        # 基本功能测试
        test_gpu_allocation()
        
        # 并发测试
        test_concurrent_allocation()
        
        # 压力测试
        test_stress_allocation()
        
        print("\n" + "="*50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
