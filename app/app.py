# 在文件开头添加环境变量设置
import os
os.environ['no_proxy'] = '*'

# 导入所需的库
import logging
import os
import uuid
import subprocess
import json
import threading
import gradio as gr
import yt_dlp  # 使用 yt-dlp 替代 youtube_dl
import time
import gc
import shutil
from gradio.themes.utils import colors
import traceback
import signal
from datetime import datetime
import sys

# 设置工程目录和下载目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 清理临时文件的函数
def cleanup_temp_files(max_age_hours=24):
    """
    清理下载目录中的临时文件
    :param max_age_hours: 文件最大保留时间（小时）
    """
    try:
        logger.info("开始清理临时文件...")
        now = time.time()
        max_age_seconds = max_age_hours * 3600
        count = 0

        for filename in os.listdir(DOWNLOADS_DIR):
            file_path = os.path.join(DOWNLOADS_DIR, filename)
            try:
                # 检查文件是否过期
                if os.path.isfile(file_path) and now - os.path.getmtime(file_path) > max_age_seconds:
                    os.remove(file_path)
                    count += 1
                    logger.debug(f"已删除临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件时出错: {file_path}, 错误: {str(e)}")

        logger.info(f"临时文件清理完成，共删除 {count} 个文件")
    except Exception as e:
        logger.error(f"清理临时文件时出错: {str(e)}")

# 设置转录工作器路径
TRANSCRIBE_WORKER_PATH = os.path.join(CURRENT_DIR, "transcribe_worker.py")
FUNASR_WORKER_PATH = os.path.join(CURRENT_DIR, "funasr_worker.py")

# 配置参数
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 14400

# 可用的模型列表
AVAILABLE_MODELS = [
    "tiny",      # 最小模型，速度最快，准确性最低
    "base",      # 小型模型，速度快，准确性一般
    "small",     # 中型模型，平衡速度和准确性
    "medium",    # 大型模型，准确性高，速度较慢
    "large-v3",  # 最大模型，准确性最高，速度最慢
]

# FunASR模型列表
FUNASR_MODELS = [
    "funasr-zh-base",     # FunASR中文基础模型
    "funasr-zh-medium",   # FunASR中文中型模型
    "funasr-zh-large",    # FunASR中文大型模型
]

# 模型描述
MODEL_DESCRIPTIONS = {
    "tiny": "Whisper最小模型，速度最快，准确性最低",
    "base": "Whisper小型模型，速度快，准确性一般",
    "small": "Whisper中型模型，平衡速度和准确性",
    "medium": "Whisper大型模型，准确性高，速度较慢",
    "large-v3": "Whisper最大模型，准确性最高，速度最慢",
    "funasr-zh-base": "阿里巴巴FunASR中文基础模型，专为中文语音识别优化",
    "funasr-zh-medium": "阿里巴巴FunASR中文中型模型，带有VAD和标点功能",
    "funasr-zh-large": "阿里巴巴FunASR中文大型模型，最高准确度的中文识别"
}

# 所有可用模型
ALL_MODELS = AVAILABLE_MODELS + FUNASR_MODELS

# 创建带有描述的模型选项
# 使用列表格式，每个选项是一个元组 (显示文本, 值)
MODEL_OPTIONS = [(f"{model} - {MODEL_DESCRIPTIONS[model]}", model) for model in ALL_MODELS]

# 默认模型名称
DEFAULT_MODEL_NAME = "medium"  # 改为medium作为默认模型，避免large-v3可能的问题

# 默认模型选项
# 找到默认模型对应的选项
DEFAULT_MODEL_INDEX = 0  # 默认使用列表中的第一个模型

# 循环查找默认模型的索引
for i, option in enumerate(MODEL_OPTIONS):
    if option[1] == DEFAULT_MODEL_NAME:
        DEFAULT_MODEL_INDEX = i
        break

# 默认模型
DEFAULT_MODEL = MODEL_OPTIONS[DEFAULT_MODEL_INDEX]

# 日志配置
LOG_FILE = os.path.join(CURRENT_DIR, "whisper_app.log")
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)

# 添加控制台处理器
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# 添加文件处理器
fh = logging.FileHandler(LOG_FILE, encoding='utf-8')
fh.setLevel(logging.INFO)
fh.setFormatter(formatter)
logger.addHandler(fh)

# GPU与信号量配置
gpu_lock = threading.Lock()
GPUS = ["0", "1"]  # 默认使用GPU0（索引为0），其次使用GPU1（索引为1）
MAX_CONCURRENT_TASKS = 2  # 将并行任务数改为2

class GPUManager:
    def __init__(self):
        self.gpu_states = {gpu: {"in_use": False, "last_used": 0} for gpu in GPUS}
        self.lock = threading.Lock()

    def acquire_gpu(self):
        with self.lock:
            # 按优先级顺序查找可用的GPU
            for gpu in GPUS:
                if not self.gpu_states[gpu]["in_use"]:
                    self.gpu_states[gpu]["in_use"] = True
                    self.gpu_states[gpu]["last_used"] = time.time()
                    logger.info(f"GPU {gpu} 已被分配")
                    return gpu
            logger.warning("所有GPU都在使用中")
            return None

    def release_gpu(self, gpu):
        with self.lock:
            if gpu in self.gpu_states:
                self.gpu_states[gpu]["in_use"] = False
                logger.info(f"GPU {gpu} 已被释放")

    def get_gpu_status(self):
        """获取GPU使用状态"""
        with self.lock:
            return {gpu: state["in_use"] for gpu, state in self.gpu_states.items()}

gpu_manager = GPUManager()

# 格式化时间函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    return seconds

# 格式化转录开头信息
def format_transcription_header(title, author, date, duration):
    # 将日期格式从YYYYMMDD转换为YYYY年MM月DD日
    date_str = f"{date[:4]}年{date[4:6]}月{date[6:]}日"

    header = f"""视频标题: {title}
作者: {author}
上传日期: {date_str}
视频时长: {duration}秒
==================================================

"""
    return header

# 设置一个环境变量来控制调试模式
DEBUG = os.environ.get('DEBUG', 'false').lower() == 'true'

# 日志记录函数
def add_log(message, log_messages=None):
    if log_messages is None:
        log_messages = []
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    log_messages.append(log_entry)
    logger.info(message)

    # 返回日志文本和 JavaScript 代码
    log_text = "\n".join(log_messages)

    # 使用HTML来显示日志
    html_logs = "<br>".join([log.replace('<', '&lt;').replace('>', '&gt;') for log in log_messages])

    js_code = f"""
    <div class="terminal-container">
        <div class="terminal-header">转录日志</div>
        <div class="terminal-body">
            {html_logs}
        </div>
    </div>
    <style>
    .terminal-container {{
        display: flex;
        flex-direction: column;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-bottom: 10px;
        overflow: hidden;
    }}
    .terminal-header {{
        background-color: #f0f0f0;
        padding: 5px 10px;
        font-weight: bold;
        border-bottom: 1px solid #ccc;
    }}
    .terminal-body {{
        background-color: #000;
        color: #00ff00;
        font-family: 'Courier New', monospace;
        padding: 10px;
        height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
        line-height: 1.5;
    }}
    </style>
    <script>
    // 自动滚动到底部
    (function() {{
        const terminals = document.querySelectorAll('.terminal-body');
        terminals.forEach(terminal => {{
            terminal.scrollTop = terminal.scrollHeight;
        }});
    }})();
    </script>
    """

    return js_code

# 转录音频文件函数
def transcribe_audio(audio_filepath, task, return_timestamps, model=DEFAULT_MODEL, progress=gr.Progress()):
    # 初始化日志输出
    log_messages = []
    # 初始化进度信息
    progress_info = "准备转录..."
    progress(0, desc=progress_info)
    log_output = add_log("准备转录...", log_messages)

    # 从模型选项中提取模型名称
    if isinstance(model, tuple) and len(model) == 2:
        # 如果是元组格式 (显示文本, 值)
        model_name = model[1]
    else:
        # 如果不是元组格式，使用默认模型
        logger.warning(f"无效的模型格式: {model}, 使用默认模型: {DEFAULT_MODEL_NAME}")
        model_name = DEFAULT_MODEL_NAME

    # 获取音频文件大小
    file_size_mb = os.path.getsize(audio_filepath) / (1024 * 1024)
    file_info = f"文件大小: {file_size_mb:.2f} MB"

    # 使用模型名称进行转录
    try:
        progress_info = f"开始转录\n模型: {model_name}\n任务: {task}\n{file_info}"
        progress(0.05, desc=progress_info)

        logger.info(f"开始转录音频文件: {audio_filepath}")
        logger.info(f"模型: {model_name}, 任务类型: {task}, 返回时间戳: {return_timestamps}")

        # 获取一个可用的GPU
        progress_info += "\n正在分配GPU资源..."
        progress(0.1, desc=progress_info)
        add_log("正在分配GPU资源...", log_messages)

        gpu_id = gpu_manager.acquire_gpu()
        if gpu_id is None:
            error_msg = "所有GPU都在使用中，请稍后再试"
            logger.error(error_msg)
            progress(0, desc=error_msg)
            add_log(error_msg, log_messages)
            raise gr.Error(error_msg)

        progress_info += f"\n已分配 GPU {gpu_id}"
        progress(0.15, desc=progress_info)
        add_log(f"已分配 GPU {gpu_id}", log_messages)
        logger.info(f"使用 GPU {gpu_id} 进行转录")

        try:
            # 准备命令行参数
            # 根据模型类型选择不同的转录工作器
            if model_name.startswith("funasr-"):
                worker_path = FUNASR_WORKER_PATH
                model_type = "FunASR"
                logger.info(f"使用FunASR转录工作器: {worker_path}")
            else:
                worker_path = TRANSCRIBE_WORKER_PATH
                model_type = "Whisper"
                logger.info(f"使用Whisper转录工作器: {worker_path}")

            progress_info += f"\n使用{model_type}模型: {model_name}"
            progress(0.2, desc=progress_info)
            log_output = add_log(f"使用{model_type}模型: {model_name}")

            cmd = [
                "python",
                worker_path,
                "--audio",
                audio_filepath,
                "--gpu",
                gpu_id,
                "--task",
                task,
                "--model",
                model_name,  # 使用用户选择的模型
                "--log_file",
                LOG_FILE  # 添加日志文件路径
            ]

            if return_timestamps:
                cmd.append("--return_timestamps")

            logger.info(f"执行命令: {' '.join(cmd)}")

            # 检查命令行参数
            logger.info(f"音频文件路径: {audio_filepath}")
            logger.info(f"音频文件大小: {os.path.getsize(audio_filepath) if os.path.exists(audio_filepath) else '文件不存在'} 字节")
            logger.info(f"转录任务类型: {task}")
            logger.info(f"是否返回时间戳: {return_timestamps}")
            logger.info(f"日志文件路径: {LOG_FILE}")
            logger.info(f"当前工作目录: {os.getcwd()}")

            # 检查Python解释器
            python_path = cmd[0]
            logger.info(f"使用的Python解释器: {python_path}")
            logger.info(f"解释器是否存在: {os.path.exists(python_path)}")

            # 检查脚本文件
            script_path = cmd[1]
            logger.info(f"转录脚本路径: {script_path}")
            logger.info(f"脚本是否存在: {os.path.exists(script_path)}")

            # 尝试获取Python版本
            try:
                python_version = subprocess.check_output([python_path, '--version'], stderr=subprocess.STDOUT, text=True)
                logger.info(f"Python版本: {python_version.strip()}")
            except Exception as e:
                logger.error(f"无法获取Python版本: {str(e)}")

            # 尝试检查whisper模块
            try:
                whisper_check = subprocess.check_output([python_path, '-c', 'import whisper; print(f"whisper版本: {whisper.__version__}")'], stderr=subprocess.STDOUT, text=True)
                logger.info(f"Whisper检查结果: {whisper_check.strip()}")
            except Exception as e:
                logger.error(f"检查whisper模块失败: {str(e)}")

            logger.info("即将启动转录进程...")
            progress_info += "\n正在加载模型..."
            progress(0.25, desc=progress_info)
            log_output = add_log("正在加载模型...")

            # 启动转录进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            logger.info(f"转录进程已启动，PID: {process.pid}")
            progress_info += f"\n转录进程已启动 (PID: {process.pid})"
            progress(0.3, desc=progress_info)
            log_output = add_log(f"转录进程已启动 (PID: {process.pid})")

            # 设置超时时间（20分钟）
            timeout = 1200
            start_time = time.time()
            output_lines = []
            error_lines = []
            process_completed = False

            try:
                # 进度跟踪变量
                model_loaded = False
                transcription_started = False
                transcription_progress = 0
                last_progress_update = time.time()

                while True:
                    # 检查进程是否已结束
                    if process.poll() is not None:
                        process_completed = True
                        break

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        logger.error(f"转录超时（{timeout}秒）")
                        process.terminate()
                        time.sleep(1)
                        if process.poll() is None:
                            process.kill()
                            logger.error("强制终止进程")
                        progress_info += "\n转录超时，请尝试处理更短的音频"
                        progress(0, desc=progress_info)
                        raise gr.Error("转录任务超时，请尝试处理更短的音频")

                    # 读取输出
                    output_line = process.stdout.readline()
                    if output_line:
                        output_line = output_line.strip()
                        output_lines.append(output_line)
                        logger.info(f"转录输出: {output_line}")

                        # 更新进度信息
                        if "loading model" in output_line.lower() or "加载模型" in output_line:
                            progress_info += "\n正在加载模型..."
                            progress(0.35, desc=progress_info)
                        elif "model loaded" in output_line.lower() or "模型加载完成" in output_line:
                            model_loaded = True
                            progress_info += "\n模型加载完成"
                            progress(0.4, desc=progress_info)
                        elif "transcribing" in output_line.lower() or "开始转录" in output_line:
                            transcription_started = True
                            progress_info += "\n开始转录音频..."
                            progress(0.5, desc=progress_info)
                        elif "%" in output_line and transcription_started:
                            # 尝试提取进度百分比
                            try:
                                percent_str = output_line.split("%")[0].split(" ")[-1]
                                percent = float(percent_str)
                                if percent > transcription_progress:
                                    transcription_progress = percent
                                    progress_value = 0.5 + (percent / 200)  # 进度从50%到100%
                                    progress_info = f"转录进度: {percent:.1f}%\n模型: {model_name}\nGPU: {gpu_id}"
                                    progress(min(0.95, progress_value), desc=progress_info)
                            except:
                                pass

                        # 定期更新进度信息，保持用户界面响应
                        if time.time() - last_progress_update > 2 and not model_loaded:
                            progress(0.35, desc=progress_info + "\n模型加载中...")
                            last_progress_update = time.time()

                        continue

                    error_line = process.stderr.readline()
                    if error_line:
                        error_line = error_line.strip()
                        error_lines.append(error_line)
                        logger.error(f"转录错误: {error_line}")

                        # 将错误信息显示给用户
                        if "error" in error_line.lower() or "错误" in error_line:
                            progress_info += f"\n错误: {error_line}"
                            progress(min(progress.value, 0.9), desc=progress_info)

                        continue

                    if not output_line and not error_line:
                        time.sleep(0.1)

                # 获取剩余输出
                remaining_output, remaining_error = process.communicate()
                if remaining_output:
                    logger.info(f"剩余输出: {remaining_output}")
                    output_lines.append(remaining_output)
                if remaining_error:
                    logger.error(f"剩余错误: {remaining_error}")
                    error_lines.append(remaining_error)

            finally:
                # 释放GPU
                with gpu_manager.lock:
                    gpu_manager.gpu_states[gpu_id]["in_use"] = False
                logger.info(f"已释放 GPU {gpu_id}")

            if process.returncode != 0:
                error_message = "".join(error_lines)
                logger.error(f"转录进程返回错误码 {process.returncode}: {error_message}")
                progress_info += f"\n转录失败: 进程返回错误码 {process.returncode}"
                progress(0, desc=progress_info)
                raise gr.Error(f"转录失败: {error_message}")

            # 解析输出
            output_text = "".join(output_lines)
            logger.info("转录完成，正在解析结果")

            # 计算转录用时
            runtime = time.time() - start_time
            formatted_runtime = f"{runtime:.2f}"

            # 更新最终进度
            progress_info = f"转录完成!\n模型: {model_name}\nGPU: {gpu_id}\n用时: {formatted_runtime}秒"
            progress(1.0, desc=progress_info)

            # 更新最终日志
            final_log = add_log(f"转录成功，用时: {formatted_runtime} 秒")

            return formatted_runtime, output_text.strip(), final_log

        except Exception as e:
            error_msg = f"转录过程出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())

            # 更新错误日志
            error_log = add_log(f"转录错误: {str(e)}")
            return "0.00", "", error_log

    except Exception as e:
        error_msg = f"转录音频时出错: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        # 更新错误日志
        try:
            error_log = add_log(f"转录错误: {str(e)}")
            return "0.00", "", error_log
        except:
            # 如果日志函数也出错，直接返回错误
            return "0.00", "", f"转录错误: {str(e)}"

# 只下载音频文件的函数
def download_yt_audio(url, output_path, progress_callback=None):
    """下载YouTube视频的音频部分"""
    try:
        # 定义一组常见的User-Agent，随机使用其中一个
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        ]

        import random
        selected_ua = random.choice(user_agents)

        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': output_path,
            'quiet': True,
            'no_warnings': True,
            'progress_hooks': [progress_callback] if progress_callback else [],
            'extract_audio': True,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'm4a',
                'preferredquality': '192',
            }],
            'http_headers': {
                'User-Agent': selected_ua,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'DNT': '1',
            },
            # 增加超时时间和重试次数
            'socket_timeout': 60,  # 增加到60秒
            'retries': 5,         # 增加重试次数
            'fragment_retries': 5, # 片段重试次数
            'skip_unavailable_fragments': True,  # 跳过不可用的片段
            'external_downloader_args': ['-timeout', '60']  # 外部下载器超时
        }

        # 添加重试机制
        max_retries = 3
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    logger.info(f"开始下载YouTube音频，使用 User-Agent: {selected_ua} (尝试 {retry_count + 1}/{max_retries})")
                    info = ydl.extract_info(url, download=True)
                    logger.info("YouTube下载完成")

                    if info is None:
                        raise Exception("无法获取视频信息")

                    # 下载成功，跳出重试循环
                    break
            except Exception as e:
                last_error = e
                retry_count += 1
                logger.warning(f"YouTube下载失败 (尝试 {retry_count}/{max_retries}): {str(e)}")

                if retry_count < max_retries:
                    # 如果还有重试机会，等待一段时间后重试
                    wait_time = 5 * retry_count  # 每次重试等待时间增加
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

                    # 切换不同的User-Agent
                    selected_ua = random.choice(user_agents)
                    ydl_opts['http_headers']['User-Agent'] = selected_ua
                    logger.info(f"切换到新的User-Agent: {selected_ua}")
                else:
                    # 所有重试都失败了，抛出异常
                    raise Exception(f"在 {max_retries} 次尝试后仍然无法下载YouTube视频: {str(last_error)}")

        # 如果所有重试都失败了，但没有抛出异常（这不应该发生）
        if retry_count >= max_retries and info is None:
            raise Exception("所有下载尝试都失败了")

        # 如果成功下载，返回视频信息
        video_info = {
            'title': info.get('title', 'Unknown'),
            'uploader': info.get('uploader', 'Unknown'),
            'upload_date': info.get('upload_date', 'Unknown'),
            'duration': info.get('duration', 0)
        }
        return True, video_info

    except Exception as e:
        error_msg = f"{str(e)}\n{traceback.format_exc()}"
        logger.error(f"YouTube下载失败: {error_msg}")
        return False, error_msg

def transcribe_youtube(yt_url, task, return_timestamps, model=DEFAULT_MODEL, progress=gr.Progress()):
    # 初始化日志输出
    log_messages = []
    # 从模型选项中提取模型名称
    if isinstance(model, tuple) and len(model) == 2:
        # 如果是元组格式 (显示文本, 值)
        model_name = model[1]
    else:
        # 如果不是元组格式，使用默认模型
        logger.warning(f"无效的模型格式: {model}, 使用默认模型: {DEFAULT_MODEL_NAME}")
        model_name = DEFAULT_MODEL_NAME
    try:
        logger.info(f"开始处理 YouTube 链接: {yt_url}")
        if not yt_url or yt_url.strip() == "":
            raise gr.Error("请输入YouTube视频链接")

        # 处理 URL 格式
        yt_url = yt_url.strip()
        if not yt_url.startswith(('http://', 'https://')):
            if yt_url.startswith('www.'):
                yt_url = 'https://' + yt_url
            else:
                yt_url = 'https://www.' + yt_url
        logger.info(f"处理后的 URL: {yt_url}")

        # 生成唯一的文件名
        unique_id = str(uuid.uuid4())
        filepath_base = os.path.join(DOWNLOADS_DIR, f"yt_{unique_id}")
        audio_filepath = f"{filepath_base}.m4a"

        # 显示下载进度的回调函数
        def yt_progress_hook(d):
            if d['status'] == 'downloading':
                try:
                    total = d.get('total_bytes') or d.get('total_bytes_estimate', 0)
                    downloaded = d.get('downloaded_bytes', 0)
                    if total > 0:
                        current_progress = downloaded / total
                        # 计算文件大小的可读格式
                        total_mb = total / (1024 * 1024)
                        downloaded_mb = downloaded / (1024 * 1024)
                        try:
                            progress(current_progress, desc=f"正在下载... {downloaded_mb:.1f}MB / {total_mb:.1f}MB ({current_progress*100:.1f}%)")
                        except Exception as e:
                            logger.warning(f"更新Gradio下载进度时出错: {e}")
                except Exception as e:
                    logger.warning(f"处理下载进度时出错: {e}")
            elif d['status'] == 'finished':
                try:
                    # 获取下载完成的文件信息
                    progress(1, desc=f"下载完成，正在处理音频文件...")

                    # 等待文件处理完成（最多等待5秒）
                    max_wait = 5
                    wait_time = 0
                    while not os.path.exists(audio_filepath) and wait_time < max_wait:
                        time.sleep(0.5)
                        wait_time += 0.5

                    # 再次检查文件并获取大小
                    if os.path.exists(audio_filepath):
                        # 等待文件写入完成
                        time.sleep(0.5)
                        file_size = os.path.getsize(audio_filepath)
                        file_size_mb = file_size / (1024 * 1024)
                        progress(1, desc=f"下载完成 ({file_size_mb:.1f}MB)。准备转录...")
                        logger.info(f"下载完成，音频文件大小: {file_size_mb:.1f}MB")
                    else:
                        progress(1, desc="下载完成，准备转录...")
                        logger.warning("下载完成但未找到音频文件")
                except Exception as e:
                    logger.warning(f"更新Gradio完成进度时出错: {e}")
                    progress(1, desc="下载完成，准备转录...")

        # 下载音频并获取视频信息
        logger.info("开始下载音频...")
        success, video_info = download_yt_audio(yt_url, filepath_base, progress_callback=yt_progress_hook)

        if not success:
            error_msg = str(video_info)
            logger.error(f"下载失败: {error_msg}")
            try:
                if os.path.exists(audio_filepath):
                    os.remove(audio_filepath)
            except Exception as cleanup_err:
                logger.warning(f"清理下载失败的文件时出错: {cleanup_err}")
            raise gr.Error(f"下载失败: {error_msg}")

        logger.info(f"音频下载完成: {audio_filepath}")
        if not os.path.exists(audio_filepath):
            logger.error(f"错误：下载声称完成，但文件 {audio_filepath} 不存在！")
            raise gr.Error(f"下载后文件丢失，请检查下载目录权限或磁盘空间。")

        # 转录音频
        logger.info("开始转录音频...")
        gpu_id = None
        process = None
        try:
            # 获取一个可用的GPU
            gpu_id = gpu_manager.acquire_gpu()
            if gpu_id is None:
                raise gr.Error("当前没有可用的GPU，请稍后再试")
            logger.info(f"已分配 GPU {gpu_id} 用于任务")

            # 根据模型类型选择不同的转录工作器
            if model_name.startswith("funasr-"):
                worker_script_path = FUNASR_WORKER_PATH
                logger.info(f"使用FunASR转录工作器: {worker_script_path}")
            else:
                worker_script_path = TRANSCRIBE_WORKER_PATH
                logger.info(f"使用Whisper转录工作器: {worker_script_path}")

            if not os.path.exists(worker_script_path):
                logger.error(f"错误：无法找到转录工作器脚本，路径: {worker_script_path}")
                raise gr.Error(f"无法找到核心转录脚本，请检查部署。")

            # 使用虚拟环境中的 Python 解释器
            # 确保使用正确的虚拟环境路径 /home/<USER>/whisper-jax/venv
            logger.info(f"将使用 Pipenv 环境的 Python 解释器: {sys.executable}")
            python_executable = sys.executable

            command = [
                python_executable,
                worker_script_path,
                "--audio", audio_filepath,
                "--gpu", str(gpu_id),
                "--task", task,
                "--model", model_name,  # 使用用户选择的模型
                "--log_file", LOG_FILE  # 添加日志文件路径
            ]
            if return_timestamps:
                command.append("--return_timestamps")

            logger.info(f"准备执行命令: {' '.join(command)}")

            # 使用临时文件来存储输出结果
            output_file = os.path.join(DOWNLOADS_DIR, f"output_{unique_id}.json")
            command.extend(["--output_file", output_file])

            logger.info(f"将使用输出文件: {output_file}")

            # 检查音频文件
            if not os.path.exists(audio_filepath):
                logger.error(f"音频文件不存在: {audio_filepath}")
                raise gr.Error(f"音频文件不存在，请重新下载")

            file_size = os.path.getsize(audio_filepath)
            logger.info(f"音频文件大小: {file_size} 字节")

            if file_size == 0:
                logger.error(f"音频文件大小为0: {audio_filepath}")
                raise gr.Error(f"下载的音频文件为空，请重新下载")

            # 检查文件格式
            try:
                import magic
                mime = magic.Magic(mime=True)
                with open(audio_filepath, 'rb') as f:
                    file_type = mime.from_buffer(f.read(2048))
                logger.info(f"检测到的文件类型: {file_type}")

                if not file_type.startswith('audio/'):
                    logger.warning(f"文件类型可能不是音频: {file_type}")
            except Exception as e:
                logger.warning(f"无法检测文件类型: {str(e)}")

            # 检查系统资源
            try:
                import psutil
                mem = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                logger.info(f"系统资源状态 - 内存: {mem.percent}% 使用, 磁盘: {disk.percent}% 使用")
            except Exception as e:
                logger.warning(f"无法获取系统资源信息: {str(e)}")

            # 设置更多的环境变量
            logger.info(f"Current PYTHONPATH for subprocess: {os.environ.get('PYTHONPATH', '')}")
            # env_vars will be a copy of the current environment (set by 'pipenv run')
            # and then updated with specific variables needed for the subprocess.
            # PYTHONPATH from 'pipenv run' will be preserved.
            env_vars = os.environ.copy()
            env_vars.update({
                'PYTHONIOENCODING': 'utf-8',  # 确保 Python 使用 UTF-8 编码
                'PYTHONUNBUFFERED': '1',      # 禁用输出缓冲
                'TF_CPP_MIN_LOG_LEVEL': '0',  # 显示所有 TensorFlow 日志
                'XLA_FLAGS': '--xla_gpu_cuda_data_dir=/usr/local/cuda',  # 指定 CUDA 路径
                'JAX_PLATFORMS': '',  # 不限制 JAX 平台
                'JAX_ENABLE_X64': '1',  # 启用 64 位精度
                'JAX_DEBUG_NANS': '1',  # 调试 NaN 值
                'JAX_LOG_COMPILES': '1'  # 记录编译信息
            })

            # 启动子进程
            logger.info("尝试启动子进程...")

            # 检查 whisper 库，但不在用户界面显示这些信息
            try:
                # 使用日志记录而不是标准输出，避免在用户界面显示
                whisper_check_cmd = [command[0], '-c', 'import whisper; import sys; sys.stderr.write(f"whisper 已安装，版本: {whisper.__version__}\n")']
                whisper_output = subprocess.check_output(whisper_check_cmd, stderr=subprocess.PIPE, text=True, env=env_vars)
                logger.info(f"Whisper检查: {whisper_output.strip()}")
            except Exception as e:
                logger.error(f"无法导入 whisper: {str(e)}")
                # 尝试获取更详细的错误信息
                try:
                    error_output = subprocess.check_output([command[0], '-c', 'import whisper'], stderr=subprocess.PIPE, text=True, env=env_vars)
                    logger.error(f"导入 whisper 的错误输出: {error_output}")
                except subprocess.CalledProcessError as e:
                    logger.error(f"导入 whisper 失败: {e.output}")

            logger.info("即将启动子进程...")
            logger.info(f"完整命令: {' '.join(command)}")
            logger.info(f"环境变量: {json.dumps({k: v for k, v in env_vars.items() if k.startswith(('PYTHON', 'PATH', 'CUDA', 'LD_LIBRARY'))}, indent=2)}")

            # 检查命令行参数
            logger.info(f"音频文件路径: {audio_filepath}")
            logger.info(f"音频文件大小: {os.path.getsize(audio_filepath) if os.path.exists(audio_filepath) else '文件不存在'} 字节")
            logger.info(f"转录任务类型: {task}")
            logger.info(f"是否返回时间戳: {return_timestamps}")
            logger.info(f"日志文件路径: {LOG_FILE}")
            logger.info(f"当前工作目录: {os.getcwd()}")

            try:
                # 先更新进度显示，然后启动进程
                progress(0.1, desc="正在启动转录进程...")
                logger.info("尝试启动子进程...")

                # 使用环境变量来抑制Python版本信息的输出
                env_vars['PYTHONIOENCODING'] = 'utf-8'
                env_vars['PYTHONUNBUFFERED'] = '1'
                env_vars['PYTHONDONTWRITEBYTECODE'] = '1'  # 抑制写入.pyc文件
                env_vars['PYTHONHASHSEED'] = '0'  # 固定hash种子

                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1,
                    universal_newlines=True,
                    env=env_vars
                )
                logger.info(f"子进程已启动 (PID: {process.pid})")
                progress(0.15, desc="转录进程已启动，正在加载模型...")

                # 等待一小段时间，检查进程是否立即结束
                time.sleep(0.5)
                if process.poll() is not None:
                    returncode = process.poll()
                    stderr_output = process.stderr.read() if process.stderr else ""
                    stdout_output = process.stdout.read() if process.stdout else ""
                    logger.error(f"子进程启动后立即结束，返回码: {returncode}")
                    logger.error(f"子进程标准输出: {stdout_output}")
                    logger.error(f"子进程错误输出: {stderr_output}")
                    raise gr.Error(f"转录进程启动失败，返回码: {returncode}\n{stderr_output}")
            except Exception as e:
                logger.error(f"启动子进程时出错: {str(e)}")
                logger.error(traceback.format_exc())
                raise gr.Error(f"启动转录进程时出错: {str(e)}")

            output_lines = []
            error_lines = []
            last_progress = 0
            last_message = "正在初始化转录..."
            progress(0, desc=last_message)

            # 发送初始转录状态信息
            try:
                progress(0.05, desc="正在准备转录模型...")
                # 设置一个标志，表示我们已经显示了初始状态
                initial_status_shown = True
            except Exception as e:
                logger.warning(f"更新初始转录状态时出错: {e}")

            # 实时读取输出
            while True:
                try:
                    output_line = process.stdout.readline() if process.stdout else ""
                    error_line = process.stderr.readline() if process.stderr else ""
                except Exception as e:
                    logger.error(f"读取子进程输出时出错: {str(e)}")
                    error_line = f"读取输出错误: {str(e)}"
                    output_line = ""

                # 记录原始输出，帮助调试
                if output_line.strip():
                    logger.debug(f"原始STDOUT: {output_line.strip()}")
                if error_line.strip():
                    logger.debug(f"原始STDERR: {error_line.strip()}")

                process_finished = process.poll() is not None

                if process_finished and not output_line and not error_line:
                    logger.info("子进程已结束，且无更多输出。")
                    # 尝试获取所有剩余输出
                    try:
                        process.communicate(timeout=1)
                    except Exception as e:
                        logger.error(f"获取子进程剩余输出时出错: {str(e)}")
                    break

                if output_line:
                    output_line = output_line.strip()
                    if output_line:
                        # 过滤掉Python版本信息等无关输出
                        if "Python" in output_line and "version" in output_line:
                            logger.debug(f"Python版本信息（已过滤）: {output_line}")
                            # 不显示这些信息给用户
                            continue

                        logger.debug(f"Worker STDOUT: {output_line}")

                        # 尝试解析JSON
                        try:
                            if output_line.startswith('{') and output_line.endswith('}'):
                                try:
                                    data = json.loads(output_line)
                                    data_type = data.get("type", "")
                                    logger.info(f"解析到JSON数据，类型: {data_type}")
                                except json.JSONDecodeError as json_err:
                                    logger.warning(f"无法解析JSON数据: {str(json_err)}\n原始数据: {output_line[:100]}...")
                                    continue

                                if data_type == "progress":
                                    current_progress = data.get("value", 0)
                                    message = data.get("message", "正在转录...")

                                    # 始终显示消息，即使进度没有变化
                                    try:
                                        progress(current_progress, desc=message)
                                        logger.info(f"更新进度: {current_progress:.2f}, 消息: {message}")

                                        # 如果进度变化超过5%或者消息变化，更新日志
                                        if abs(current_progress - last_progress) >= 0.05 or message != last_message:
                                            log_output = add_log(f"{message} ({current_progress*100:.1f}%)")
                                    except Exception as e:
                                        logger.warning(f"更新Gradio转录进度时出错: {e}")
                                    last_progress = current_progress
                                    last_message = message

                                elif data_type == "info" or data_type == "debug":
                                    message = data.get("message", "")
                                    if message and message != last_message:
                                        try:
                                            progress(last_progress, desc=message)
                                            # 更新日志
                                            log_output = add_log(message)
                                        except Exception as e:
                                            logger.warning(f"更新Gradio描述信息时出错: {e}")
                                    last_message = message
                                    logger.info(f"转录信息: {message}")

                                elif data_type == "error":
                                    error_msg = data.get("message", "未知转录错误")
                                    error_traceback = data.get("traceback", "")
                                    memory_info = data.get("memory_info", {})
                                    logger.error(f"转录Worker报告错误: {error_msg}\nTraceback: {error_traceback}\n内存: {json.dumps(memory_info, ensure_ascii=False)}")
                                    # 更新日志
                                    log_output = add_log(f"转录错误: {error_msg}")
                                    raise gr.Error(f"转录失败: {error_msg}")

                                elif data_type == "result":
                                    logger.info("收到最终结果数据")
                                    output_lines.append(data)
                                    # 更新日志
                                    log_output = add_log("转录完成，正在处理结果...")
                            else:
                                # 非JSON输出，仅记录到日志，不显示给用户
                                logger.debug(f"非JSON输出: {output_line}")
                        except json.JSONDecodeError:
                            # 非JSON输出，仅记录到日志，不显示给用户
                            logger.debug(f"无法解析的输出: {output_line}")
                        except Exception as parse_err:
                            logger.error(f"解析Worker输出时出错: {parse_err}\n原始行: {output_line}")
                            error_lines.append(f"解析Worker输出错误: {parse_err}")

                if error_line:
                    error_line = error_line.strip()
                    if error_line:
                        error_lines.append(error_line)
                        logger.error(f"Worker STDERR: {error_line}")

                if process_finished and not output_line and not error_line:
                    # 增加等待时间，确保所有输出都被读取
                    time.sleep(0.1)
                    # 再次检查是否有输出
                    output_line = process.stdout.readline() if process.stdout else ""
                    error_line = process.stderr.readline() if process.stderr else ""
                    if output_line or error_line:
                        # 如果还有输出，继续处理
                        continue

            logger.info("子进程通讯循环结束。")

            # 获取最终返回码
            return_code = process.wait()
            logger.info(f"子进程退出码: {return_code}")

            if return_code != 0:
                error_message = "\n".join(error_lines) if error_lines else "子进程返回非零退出码，但无错误输出。"
                logger.error(f"转录进程返回错误码 {return_code}: {error_message}")
                raise gr.Error(f"转录失败: {error_message}")
            elif error_lines:
                logger.warning(f"转录进程返回码为0，但stderr包含信息:\n" + "\n".join(error_lines))

            # 解析最终结果
            logger.info("转录完成，正在解析结果")
            final_result = None

            # 等待完成标记文件出现
            done_file = output_file + ".done"
            wait_start = time.time()
            wait_timeout = 300  # 5分钟超时
            check_interval = 1  # 每秒检查一次

            logger.info(f"等待转录完成标记文件: {done_file}")

            # 定期更新进度显示
            last_update = time.time()
            update_interval = 2  # 每2秒更新一次进度

            while not os.path.exists(done_file):
                # 检查是否超时
                if time.time() - wait_start > wait_timeout:
                    logger.error(f"等待转录完成超时 ({wait_timeout} 秒)")
                    raise gr.Error("转录超时，请尝试处理更短的音频")

                # 检查进程是否还在运行
                if process.poll() is not None:
                    # 进程已结束，但没有生成完成标记
                    if process.returncode != 0:
                        error_message = "\n".join(error_lines) if error_lines else "转录进程异常终止"
                        logger.error(f"转录进程返回错误码 {process.returncode}: {error_message}")
                        raise gr.Error(f"转录失败: {error_message}")

                    # 如果进程正常结束但没有生成完成标记，继续等待一段时间
                    logger.warning("转录进程已结束，但完成标记文件尚未生成，继续等待...")

                # 定期更新进度显示
                if time.time() - last_update > update_interval:
                    elapsed = time.time() - wait_start
                    progress_value = min(0.9, elapsed / wait_timeout)
                    progress_percent = progress_value * 100
                    progress_message = f"正在转录... {progress_percent:.1f}% (已等待 {int(elapsed)} 秒)"
                    try:
                        progress(progress_value, desc=progress_message)
                        # 每10秒更新一次日志
                        if int(elapsed) % 10 == 0:
                            log_output = add_log(progress_message)
                    except Exception as e:
                        logger.warning(f"更新进度显示时出错: {e}")
                    last_update = time.time()

                # 等待一段时间再检查
                time.sleep(check_interval)

            logger.info("检测到完成标记文件，转录已完成")

            # 从输出文件中读取结果
            if os.path.exists(output_file):
                try:
                    logger.info(f"从文件读取结果: {output_file}")
                    with open(output_file, 'r', encoding='utf-8') as f:
                        file_content = f.read()
                        if file_content.strip():
                            final_result = json.loads(file_content)
                            logger.info("成功从文件读取到结果")
                except Exception as e:
                    logger.error(f"从文件读取结果时出错: {str(e)}")
            else:
                logger.warning(f"输出文件不存在: {output_file}")

            # 如果从文件读取失败，尝试从进程输出中解析
            if final_result is None:
                # 输出所有收集到的行，帮助调试
                logger.info(f"共收集到 {len(output_lines)} 行输出")
                for i, line in enumerate(output_lines):
                    if isinstance(line, dict):
                        logger.info(f"JSON输出[{i}]: {json.dumps(line, ensure_ascii=False)[:200]}...")
                    else:
                        logger.info(f"非JSON输出[{i}]: {str(line)[:200]}...")

                # 尝试找到结果数据
                for item in output_lines:
                    if isinstance(item, dict) and item.get("type") == "result":
                        final_result = item
                        logger.info("找到有效的结果数据")
                        break

                if final_result is None:
                    logger.error("未在子进程输出中找到有效的 'result' 类型 JSON 对象。")

                    # 尝试从原始输出中解析结果
                    for line in output_lines:
                        if isinstance(line, str) and "\"type\": \"result\"" in line:
                            logger.info(f"尝试从原始字符串解析结果: {line[:100]}...")
                            try:
                                final_result = json.loads(line)
                                if final_result.get("type") == "result":
                                    logger.info("从原始字符串成功解析到结果")
                                    break
                            except:
                                pass

                    if final_result is None:
                        # 仍然没有找到结果，输出详细错误信息
                        non_json_output = "\n".join([str(line) for line in output_lines if not isinstance(line, dict)])
                        if non_json_output:
                            logger.error(f"子进程非JSON输出内容:\n{non_json_output[:500]}...")

                        # 如果有错误行，显示错误信息
                        if error_lines:
                            error_msg = "\n".join(error_lines)
                            logger.error(f"子进程错误输出:\n{error_msg}")
                            raise gr.Error(f"转录过程出错: {error_msg[:200]}")
                        else:
                            raise gr.Error("转录完成，但未能解析有效的输出结果。请检查日志了解详情。")

            runtime = final_result.get("runtime", 0)
            text_result = final_result.get("text", "")

            # 格式化转录用时，确保显示为秒数
            formatted_runtime = f"{runtime:.2f}" if isinstance(runtime, (int, float)) else "0.00"
            logger.info(f"转录成功，用时: {formatted_runtime} 秒")

            # 更新最终日志
            final_log = add_log(f"转录成功，用时: {formatted_runtime} 秒", log_messages)

            return formatted_runtime, text_result, final_log

        except Exception as e:
            error_msg = f"处理YouTube转录时出错: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())

            # 更新错误日志
            error_log = add_log(f"转录错误: {str(e)}", log_messages)

            if isinstance(e, gr.Error):
                return "0.00", "", error_log
            else:
                return "0.00", "", error_log

        finally:
            # 释放GPU
            if gpu_id is not None:
                gpu_manager.release_gpu(gpu_id)
                logger.info(f"已释放 GPU {gpu_id}")
                # 更新日志
                try:
                    add_log(f"已释放 GPU {gpu_id}", log_messages)
                except:
                    pass

            # 确保子进程被终止
            if process and process.poll() is None:
                logger.warning("子进程仍在运行，尝试终止...")
                try:
                    process.terminate()
                    time.sleep(1)
                    if process.poll() is None:
                        logger.warning("子进程未能终止，强制杀死...")
                        process.kill()
                        logger.info("子进程已被强制杀死。")
                    else:
                        logger.info("子进程已终止。")
                except Exception as term_err:
                    logger.error(f"终止子进程时出错: {term_err}")

            # 清理临时文件
            try:
                # 删除音频文件
                if os.path.exists(audio_filepath):
                    os.remove(audio_filepath)
                    logger.info(f"临时音频文件已删除: {audio_filepath}")
                else:
                    logger.warning(f"尝试删除临时音频文件，但文件不存在: {audio_filepath}")

                # 删除输出文件及相关文件
                for file_path in [output_file, output_file + ".tmp", output_file + ".done"]:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"临时文件已删除: {file_path}")
                    else:
                        logger.debug(f"尝试删除临时文件，但文件不存在: {file_path}")

                # 清理其他过期的临时文件
                cleanup_temp_files(max_age_hours=1)  # 清理超过1小时的临时文件
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

    except Exception as e:
        error_msg = f"转录 YouTube 链接时发生严重错误: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        raise gr.Error(error_msg)

# 添加任务超时监控机制
TASK_TIMEOUT = 600  # 增加到 10 分钟超时
task_start_time = None
current_task = None

class TaskMonitor:
    def __init__(self, timeout=TASK_TIMEOUT):
        self.timeout = timeout
        self.start_time = None
        self.monitoring = False
        self.monitor_thread = None

    def start(self):
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_task, daemon=True)
        self.monitor_thread.start()

    def stop(self):
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def _monitor_task(self):
        while self.monitoring:
            if time.time() - self.start_time > self.timeout:
                logger.warning("主进程任务超时，正在终止程序...")
                # 发送SIGTERM信号给自己（主进程）
                os.kill(os.getpid(), signal.SIGTERM)
                break
            time.sleep(1)

# 添加信号处理函数
def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，正在清理资源...")
    # 释放所有GPU信号量
    for gpu in GPUS:
        try:
            gpu_manager.release_gpu(gpu)
        except:
            pass
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

def check_ytdlp_update():
    try:
        logger.info("开始检查 yt-dlp 版本...")

        # 获取当前版本
        try:
            current_version_process = subprocess.run([sys.executable, "-m", "yt_dlp", "--version"],
                                                capture_output=True, text=True)
            current_version = current_version_process.stdout.strip()
            logger.info(f"当前 yt-dlp 版本: {current_version}")
        except Exception as e:
            logger.error(f"获取当前 yt-dlp 版本失败: {str(e)}")
            return f"无法获取当前 yt-dlp 版本: {str(e)}"

        # 检查是否有新版本
        logger.info("检查 yt-dlp 是否有新版本...")
        check_process = subprocess.run([sys.executable, "-m", "pip", "list", "--outdated"],
                                    capture_output=True, text=True)

        if "yt-dlp" not in check_process.stdout:
            logger.info("当前 yt-dlp 已是最新版本")
            return f"yt-dlp 已是最新版本 ({current_version})。无需更新。"

        # 备份当前版本
        logger.info("备份当前 yt-dlp 版本...")
        backup_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        # 定位 yt-dlp 包的位置
        try:
            import yt_dlp
            ytdlp_path = os.path.dirname(yt_dlp.__file__)
            backup_path = os.path.join(backup_dir, f"yt_dlp_backup_{current_version}_{int(time.time())}")

            # 创建备份
            shutil.copytree(ytdlp_path, backup_path)
            logger.info(f"yt-dlp 备份完成: {backup_path}")
        except Exception as e:
            logger.error(f"备份 yt-dlp 失败: {str(e)}")
            return f"备份 yt-dlp 失败: {str(e)}"

        # 更新 yt-dlp
        logger.info("开始更新 yt-dlp...")
        venv_pip = os.path.join(os.path.dirname(sys.executable), "pip")
        process = subprocess.run([venv_pip, "install", "--upgrade", "yt-dlp"],
                            capture_output=True, text=True)

        update_message = ""
        if process.returncode == 0:
            update_message = f"yt-dlp 更新成功！\n从 {current_version} 更新到最新版本\n"
        else:
            update_message = "yt-dlp 更新失败：\n" + process.stderr
            logger.error(update_message)
            return update_message

        # 验证更新后的 yt-dlp 是否可用
        logger.info("验证更新后的 yt-dlp...")
        try:
            # 重新加载 yt_dlp 模块
            if "yt_dlp" in sys.modules:
                del sys.modules["yt_dlp"]
            import yt_dlp

            # 测试基本功能
            test_process = subprocess.run([sys.executable, "-m", "yt_dlp", "--version"],
                                        capture_output=True, text=True)
            new_version = test_process.stdout.strip()
            logger.info(f"更新后的 yt-dlp 版本: {new_version}")
            update_message += f"\n更新后的版本: {new_version}"
        except Exception as e:
            logger.error(f"验证更新后的 yt-dlp 失败: {str(e)}")

            # 尝试回滚
            logger.warning(f"尝试回滚到备份版本: {backup_path}")
            try:
                # 删除当前版本
                shutil.rmtree(ytdlp_path)
                # 恢复备份版本
                shutil.copytree(backup_path, ytdlp_path)
                logger.info("成功回滚到备份版本")
                return f"yt-dlp 更新失败，已回滚到备份版本 ({current_version})\n错误: {str(e)}"
            except Exception as rollback_error:
                logger.error(f"回滚失败: {str(rollback_error)}")
                return f"yt-dlp 更新失败，回滚也失败\n错误: {str(e)}\n回滚错误: {str(rollback_error)}"

        logger.info("更新完成，准备重启服务...")

        # 获取当前脚本的绝对路径
        current_script = os.path.abspath(__file__)
        start_script = os.path.join(os.path.dirname(os.path.dirname(current_script)), "start_whisper_jax.sh")

        if not os.path.exists(start_script):
            error_msg = f"错误：找不到启动脚本 {start_script}"
            logger.error(error_msg)
            return update_message + "\n" + error_msg

        try:
            # 使用 nohup 在后台执行重启命令
            restart_cmd = f"bash {start_script} restart"
            subprocess.Popen(["nohup", "bash", "-c", restart_cmd],
                         stdout=subprocess.DEVNULL,
                         stderr=subprocess.DEVNULL,
                         start_new_session=True)

            return update_message + "\n服务正在重启，请等待约10秒后刷新页面..."

        except Exception as e:
            error_msg = f"重启服务失败：{str(e)}"
            logger.error(error_msg)
            return update_message + "\n" + error_msg

    except Exception as e:
        error_msg = f"更新过程中发生错误：{str(e)}\n"
        error_msg += traceback.format_exc()
        logger.error(error_msg)
        return error_msg

# 创建Gradio界面
def create_gradio_interface():
    with gr.Blocks(theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎙️ Whisper 转录服务")

        with gr.Tabs():
            # YouTube 标签页
            with gr.Tab("YouTube"):
                groups = []
                for i in range(10):
                    with gr.Group():
                        gr.Markdown(f"### 转录任务 {i+1}")
                        yt_url = gr.Text(label="YouTube视频链接")
                        task = gr.Radio(
                            choices=["transcribe", "translate"],
                            value="transcribe",
                            label="任务类型"
                        )
                        return_timestamps = gr.Checkbox(
                            value=False,  # 默认关闭时间戳功能
                            label="显示时间戳 (至少10秒间隔)"
                        )
                        model_dropdown = gr.Dropdown(
                            choices=MODEL_OPTIONS,
                            value=DEFAULT_MODEL,
                            label="转录模型"
                        )
                        transcription_time = gr.Text(
                            label="转录用时 (秒)",
                            interactive=False
                        )
                        transcription = gr.Text(
                            label="转录结果",
                            interactive=False,
                            lines=10
                        )
                        # 添加日志显示区域
                        log_output = gr.HTML(label="转录日志")

                        transcribe_button = gr.Button("开始转录")
                        transcribe_button.click(
                            fn=transcribe_youtube,
                            inputs=[yt_url, task, return_timestamps, model_dropdown],
                            outputs=[transcription_time, transcription, log_output],
                            show_progress=True
                        )
                        if i < 9:
                            gr.Markdown("---")
                        groups.append((yt_url, transcription))

            # 麦克风 标签页
            with gr.Tab("麦克风"):
                with gr.Column():
                    audio_file = gr.Audio(type="filepath", label="音频文件")
                    task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                    return_timestamps_mic = gr.Checkbox(value=False, label="显示时间戳 (至少10秒间隔)")
                    model_dropdown_mic = gr.Dropdown(
                        choices=MODEL_OPTIONS,
                        value=DEFAULT_MODEL,
                        label="转录模型"
                    )
                    transcribe_btn_mic = gr.Button("转录")
                    transcription_time_mic = gr.Text(label="转录时间 (秒)", interactive=False)
                    transcription_mic = gr.Text(label="转录内容", lines=30, show_copy_button=True)

                    # 添加日志显示区域
                    log_output_mic = gr.HTML(label="转录日志")

                    transcribe_btn_mic.click(
                        fn=transcribe_audio,
                        inputs=[audio_file, task_mic, return_timestamps_mic, model_dropdown_mic],
                        outputs=[transcription_time_mic, transcription_mic, log_output_mic],
                        show_progress=True
                    )

            # 音频文件 标签页
            with gr.Tab("音频文件"):
                with gr.Column():
                    upload_audio = gr.Audio(label="音频文件", type="filepath")
                    task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                    return_timestamps_upload = gr.Checkbox(value=False, label="显示时间戳 (至少10秒间隔)")
                    model_dropdown_upload = gr.Dropdown(
                        choices=MODEL_OPTIONS,
                        value=DEFAULT_MODEL,
                        label="转录模型"
                    )
                    transcribe_btn_upload = gr.Button("转录")
                    transcription_time_upload = gr.Text(label="转录时间 (秒)", interactive=False)
                    transcription_upload = gr.Text(label="转录内容", lines=30, show_copy_button=True)

                    # 添加日志显示区域
                    log_output_upload = gr.HTML(label="转录日志")

                    transcribe_btn_upload.click(
                        fn=transcribe_audio,
                        inputs=[upload_audio, task_upload, return_timestamps_upload, model_dropdown_upload],
                        outputs=[transcription_time_upload, transcription_upload, log_output_upload],
                        show_progress=True
                    )

            # 设置 标签页
            with gr.Tab("设置"):
                with gr.Group():
                    gr.Markdown("### YouTube下载器设置")

                    # 显示当前 yt-dlp 版本
                    try:
                        current_version_process = subprocess.run([sys.executable, "-m", "yt_dlp", "--version"],
                                                            capture_output=True, text=True)
                        current_version = current_version_process.stdout.strip()
                    except Exception:
                        current_version = "无法获取版本信息"

                    gr.Markdown(f"**当前 yt-dlp 版本:** {current_version}")
                    gr.Markdown("更新 yt-dlp 可以解决一些 YouTube 视频下载问题。更新后会自动重启服务。")

                    with gr.Row():
                        update_button = gr.Button("检查并更新 yt-dlp", variant="primary")
                        test_button = gr.Button("测试 yt-dlp 功能")

                    update_status = gr.Textbox(label="状态信息", interactive=False, lines=5)

                    # 添加测试功能
                    def test_ytdlp():
                        try:
                            # 测试获取视频信息
                            test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # 一个测试视频
                            logger.info(f"测试 yt-dlp 功能，获取视频信息: {test_url}")

                            with yt_dlp.YoutubeDL({"quiet": True, "no_warnings": True}) as ydl:
                                info = ydl.extract_info(test_url, download=False)

                            if info and "title" in info:
                                return f"yt-dlp 功能正常\n成功获取视频信息: {info['title']}\n时长: {info.get('duration_string', 'N/A')}\n上传者: {info.get('uploader', 'N/A')}"
                            else:
                                return "测试失败: 无法获取视频信息"
                        except Exception as e:
                            logger.error(f"测试 yt-dlp 功能失败: {str(e)}")
                            return f"yt-dlp 功能测试失败: {str(e)}\n{traceback.format_exc()}"

                    update_button.click(
                        fn=check_ytdlp_update,
                        inputs=[],
                        outputs=[update_status]
                    )

                    test_button.click(
                        fn=test_ytdlp,
                        inputs=[],
                        outputs=[update_status]
                    )

                    gr.Markdown("---")
                    gr.Markdown("### GPU状态监控")

                    def get_gpu_status():
                        """获取GPU使用状态"""
                        status = gpu_manager.get_gpu_status()
                        status_text = "**GPU使用状态:**\n"
                        for gpu_id, in_use in status.items():
                            status_icon = "🔴" if in_use else "🟢"
                            status_text += f"- GPU {gpu_id}: {status_icon} {'使用中' if in_use else '空闲'}\n"
                        status_text += f"\n**最大并发任务数:** {MAX_CONCURRENT_TASKS}"
                        return status_text

                    gpu_status_display = gr.Markdown(get_gpu_status())
                    refresh_gpu_status = gr.Button("刷新GPU状态")

                    refresh_gpu_status.click(
                        fn=get_gpu_status,
                        inputs=[],
                        outputs=[gpu_status_display]
                    )

                    gr.Markdown("---")
                    gr.Markdown("### 模型信息")
                    gr.Markdown("""
                    **可用模型:**
                    - **Whisper 模型:** tiny, base, small, medium, large-v3
                    - **FunASR 模型:** funasr-zh-base, funasr-zh-medium, funasr-zh-large

                    **模型特点:**
                    - Whisper 模型支持多种语言，模型越大越准确但越慢
                    - FunASR 模型由阿里巴巴达摩实验室开发，专门为中文语音识别优化
                    """)

        gr.Markdown("Whisper模型由OpenAI提供，FunASR模型由阿里巴巴达摩实验室提供。最后更新：2025年4月9日")
    return demo

def main():
    # 创建Gradio界面
    interface = create_gradio_interface()

    # 添加优雅关闭处理
    def graceful_shutdown(*args):
        logger.info("正在关闭服务...")
        # 释放所有GPU信号量
        for gpu in GPUS:
            try:
                gpu_manager.release_gpu(gpu)
            except:
                pass
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGTERM, graceful_shutdown)
    signal.signal(signal.SIGINT, graceful_shutdown)

    # 启动服务
    try:
        logger.info("正在启动服务...")

        # 启动时清理临时文件
        cleanup_temp_files(max_age_hours=24)

        interface.queue(
            max_size=20  # 队列最大大小
        ).launch(
            server_name="0.0.0.0",
            server_port=7850,
            share=False,
            show_error=True,
            max_threads=MAX_CONCURRENT_TASKS * 2,  # 增加线程池大小
            prevent_thread_lock=True
        )
        logger.info("服务启动完成")
        # 保持主线程运行
        while True:
            time.sleep(1)
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
