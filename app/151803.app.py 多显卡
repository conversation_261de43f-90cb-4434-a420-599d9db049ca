# app.py
import logging
import math
import os
import uuid
import subprocess
import json
import tempfile
import threading

import gradio as gr  # 确保已安装 Gradio 4.44.1 或更高版本
import yt_dlp as youtube_dl  # 导入 yt_dlp 作为 youtube_dl
from transformers.pipelines.audio_utils import ffmpeg_read

# 设置下载目录为工程目录下的 'downloads' 文件夹
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 设置 transcribe_worker.py 的路径
TRANSCRIBE_WORKER_PATH = os.path.join(CURRENT_DIR, "transcribe_worker.py")

# 配置不同参数
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 7200  # YouTube文件长度限制为2小时

# 应用程序标题和描述
title = "Whisper JAX: The Fastest Whisper API ⚡️"
description = """Whisper JAX是OpenAI Whisper模型的优化实现。它在JAX上运行，后端使用TPU v4-8。相较于PyTorch在A100 GPU上的运行速度，它快了超过70倍。"""
article = "Whisper large-v3模型由OpenAI提供。后端通过TRC计划支持的TPU v4-8运行。"

# 设置日志记录
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# 线程锁和GPU索引
gpu_lock = threading.Lock()
next_gpu = 0
GPUS = ["0", "1"]  # GPU0 和 GPU1

# 为每个 GPU 创建一个信号量，限制每个 GPU 的并行任务数
MAX_CONCURRENT_TASKS_PER_GPU = 2
gpu_semaphores = {
    "0": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
    "1": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
}

# 格式化时间戳函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    else:
        return seconds  # 处理格式错误的时间戳

# 转录音频文件的函数
def transcribe_audio(audio_filepath, task, return_timestamps):
    global next_gpu
    if audio_filepath is None:
        logger.warning("未提交音频文件")
        raise gr.Error("未提交音频文件！请上传音频文件后再提交请求。")

    file_size_mb = os.stat(audio_filepath).st_size / (1024 * 1024)
    if file_size_mb > FILE_LIMIT_MB:
        logger.warning("文件大小超过限制")
        raise gr.Error(
            f"文件大小超过限制。当前文件大小为 {file_size_mb:.2f}MB，限制为 {FILE_LIMIT_MB}MB。"
        )

    # 获取当前任务应该使用的 GPU
    with gpu_lock:
        current_gpu = GPUS[next_gpu]
        next_gpu = (next_gpu + 1) % len(GPUS)

    # 获取相应 GPU 的信号量
    gpu_semaphore = gpu_semaphores[current_gpu]
    logger.info(f"等待 GPU {current_gpu} 的信号量...")
    gpu_semaphore.acquire()
    logger.info(f"已获取 GPU {current_gpu} 的信号量")

    try:
        # 复制环境变量并设置 CUDA_VISIBLE_DEVICES
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = current_gpu

        # 调用子进程处理转录
        process = subprocess.run(
            [
                "python", TRANSCRIBE_WORKER_PATH,
                audio_filepath,
                task,
                str(return_timestamps)
            ],
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            env=env  # 设置环境变量
        )
    except subprocess.TimeoutExpired:
        logger.error("转录任务超时")
        raise gr.Error("转录任务超时，请稍后再试。")
    finally:
        # 无论成功还是失败，都释放信号量
        gpu_semaphore.release()
        logger.info(f"已释放 GPU {current_gpu} 的信号量")

    if process.returncode != 0:
        logger.error(f"转录错误: {process.stderr}")
        raise gr.Error(f"转录错误: {process.stderr}")

    try:
        result = json.loads(process.stdout)
        if "error" in result:
            logger.error(f"转录错误: {result['error']}")
            raise gr.Error(f"转录错误: {result['error']}")
        return result["text"], result["runtime"]
    except json.JSONDecodeError:
        logger.error("无法解析转录结果")
        raise gr.Error("无法解析转录结果。请检查日志以获取更多信息。")

# 下载YouTube音频
def download_yt_audio(yt_url, filename, progress_callback=None):
    ydl_opts = {
        "outtmpl": filename,
        "format": "worstvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
        "progress_hooks": [progress_callback] if progress_callback else [],
    }
    with youtube_dl.YoutubeDL(ydl_opts) as ydl:
        try:
            ydl.download([yt_url])
        except youtube_dl.utils.ExtractorError as err:
            raise gr.Error(str(err))
        except Exception as e:
            raise gr.Error(f"下载失败: {e}")

# 转录来自YouTube的视频
def transcribe_youtube(yt_url, task, return_timestamps, progress=None):
    global next_gpu
    if yt_url is None or yt_url.strip() == "":
        logger.warning("未提交YouTube URL")
        raise gr.Error("未提交YouTube URL！请粘贴YouTube视频的URL后再提交请求。")

    # 生成唯一文件名
    unique_id = uuid.uuid4().hex
    filepath = os.path.join(DOWNLOADS_DIR, f"video_{unique_id}.mp4")

    # 定义进度回调函数
    def yt_progress_hook(d):
        if d['status'] == 'downloading':
            total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
            downloaded_bytes = d.get('downloaded_bytes', 0)
            if total_bytes:
                percentage = downloaded_bytes / total_bytes
                speed = d.get('speed')
                if speed is not None and speed > 0:
                    speed_mib_s = speed / (1024 * 1024)
                    speed_str = f"{speed_mib_s:.2f} MiB/s"
                else:
                    speed_str = "未知速度"
                if progress is not None:
                    progress.update(percentage=percentage, desc=f"下载中... {percentage*100:.2f}% at {speed_str}")
        elif d['status'] == 'finished':
            if progress is not None:
                progress.update(percentage=1, desc="下载完成。")

    # 下载YouTube音频并更新进度
    try:
        download_yt_audio(yt_url, filepath, progress_callback=yt_progress_hook)
    except gr.Error as e:
        raise e
    except Exception as e:
        logger.error(f"下载失败: {e}")
        raise gr.Error(f"下载失败: {e}")

    # 获取当前任务应该使用的 GPU
    with gpu_lock:
        current_gpu = GPUS[next_gpu]
        next_gpu = (next_gpu + 1) % len(GPUS)

    # 获取相应 GPU 的信号量
    gpu_semaphore = gpu_semaphores[current_gpu]
    logger.info(f"等待 GPU {current_gpu} 的信号量...")
    gpu_semaphore.acquire()
    logger.info(f"已获取 GPU {current_gpu} 的信号量")

    try:
        # 复制环境变量并设置 CUDA_VISIBLE_DEVICES
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = current_gpu

        # 调用子进程处理转录
        process = subprocess.run(
            [
                "python", TRANSCRIBE_WORKER_PATH,
                filepath,
                task,
                str(return_timestamps)
            ],
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            env=env  # 设置环境变量
        )
    except subprocess.TimeoutExpired:
        logger.error("转录任务超时")
        raise gr.Error("转录任务超时，请稍后再试。")
    finally:
        # 无论成功还是失败，都释放信号量
        gpu_semaphore.release()
        logger.info(f"已释放 GPU {current_gpu} 的信号量")

    if process.returncode != 0:
        logger.error(f"转录错误: {process.stderr}")
        raise gr.Error(f"转录错误: {process.stderr}")

    try:
        result = json.loads(process.stdout)
        if "error" in result:
            logger.error(f"转录错误: {result['error']}")
            raise gr.Error(f"转录错误: {result['error']}")
        text = result["text"]
        runtime = result["runtime"]
    except json.JSONDecodeError:
        logger.error("无法解析转录结果")
        raise gr.Error("无法解析转录结果。请检查日志以获取更多信息。")

    # 可选：处理完毕后删除下载的文件以节省磁盘空间
    try:
        os.remove(filepath)
        logger.info(f"已删除临时文件: {filepath}")
    except OSError as e:
        logger.warning(f"无法删除临时文件 {filepath}: {e}")

    return runtime, text

# 定义整个应用的界面
def create_gradio_interface():
    with gr.Blocks() as demo:
        gr.Markdown(f"## {title}")
        gr.Markdown(description)

        # YouTube 标签页
        with gr.Tab("YouTube"):
            with gr.Column():
                yt_url = gr.Textbox(lines=1, placeholder="在此粘贴YouTube视频的URL", label="YouTube URL")
                task = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn = gr.Button("转录")
                transcription_time = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn.click(
                    fn=transcribe_youtube,
                    inputs=[yt_url, task, return_timestamps],
                    outputs=[transcription_time, transcription],
                    show_progress=True  # 启用进度显示
                )

        # Microphone 标签页
        with gr.Tab("麦克风"):
            with gr.Column():
                audio_file = gr.Audio(type="filepath", label="音频文件")
                task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_mic = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn_mic = gr.Button("转录")
                transcription_time_mic = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_mic = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn_mic.click(
                    fn=lambda audio, task, rt: transcribe_audio(audio, task, rt),
                    inputs=[audio_file, task_mic, return_timestamps_mic],
                    outputs=[transcription_time_mic, transcription_mic]
                )

        # Audio File 标签页
        with gr.Tab("音频文件"):
            with gr.Column():
                upload_audio = gr.Audio(label="音频文件", type="filepath")
                task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_upload = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn_upload = gr.Button("转录")
                transcription_time_upload = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_upload = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn_upload.click(
                    fn=lambda audio, task, rt: transcribe_audio(audio, task, rt),
                    inputs=[upload_audio, task_upload, return_timestamps_upload],
                    outputs=[transcription_time_upload, transcription_upload]
                )

        gr.Markdown(article)
    return demo

def main():
    # 创建 Gradio 界面
    demo = create_gradio_interface()

    # 启动 Gradio 应用
    demo.queue(max_size=10)  # 根据需要调整队列大小
    demo.launch(server_name="0.0.0.0", show_api=False)

if __name__ == "__main__":
    main()
