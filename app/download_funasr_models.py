#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下载FunASR模型的脚本
"""

import os
import sys
import argparse
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("download-funasr")

try:
    from funasr import AutoModel
except ImportError:
    logger.error("未安装FunASR库，请先安装：pip install funasr")
    sys.exit(1)

# FunASR模型ID
FUNASR_MODELS = {
    "funasr-zh-base": "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
    "funasr-zh-medium": "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
    "funasr-zh-large": "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
}

def download_model(model_name, force=False):
    """下载指定的FunASR模型"""
    if model_name not in FUNASR_MODELS:
        logger.error(f"未知的模型名称: {model_name}")
        logger.info(f"可用的模型: {', '.join(FUNASR_MODELS.keys())}")
        return False
    
    model_id = FUNASR_MODELS[model_name]
    logger.info(f"开始下载模型: {model_name} (ID: {model_id})")
    
    try:
        # 使用AutoModel下载模型
        start_time = time.time()
        model = AutoModel(model=model_id, model_revision="v1.0.4", device="cpu")
        end_time = time.time()
        
        logger.info(f"模型 {model_name} 下载完成，用时 {end_time - start_time:.2f} 秒")
        return True
    except Exception as e:
        logger.error(f"下载模型 {model_name} 时出错: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="下载FunASR模型")
    parser.add_argument("--model", type=str, choices=list(FUNASR_MODELS.keys()) + ["all"], default="all",
                        help="要下载的模型名称，或'all'下载所有模型")
    parser.add_argument("--force", action="store_true", help="强制重新下载模型，即使已存在")
    
    args = parser.parse_args()
    
    if args.model == "all":
        logger.info("准备下载所有FunASR模型")
        success = True
        for model_name in FUNASR_MODELS:
            if not download_model(model_name, args.force):
                success = False
        
        if success:
            logger.info("所有模型下载完成")
            return 0
        else:
            logger.error("部分模型下载失败")
            return 1
    else:
        if download_model(args.model, args.force):
            logger.info(f"模型 {args.model} 下载完成")
            return 0
        else:
            logger.error(f"模型 {args.model} 下载失败")
            return 1

if __name__ == "__main__":
    sys.exit(main())
