# 导入所需的库
import logging
import os
import uuid
import subprocess
import json
import threading
import gradio as gr
import yt_dlp as youtube_dl  # 使用 yt_dlp 替代 youtube_dl
import time
import gc
from gradio.themes.utils import colors
import traceback
import signal
from datetime import datetime

# 设置工程目录和下载目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 设置transcribe_worker.py的路径
TRANSCRIBE_WORKER_PATH = os.path.join(CURRENT_DIR, "transcribe_worker.py")

# 配置参数
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 14400

# 日志配置
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# GPU与信号量配置
gpu_lock = threading.Lock()
next_gpu = 0
GPUS = ["0", "1"]
MAX_CONCURRENT_TASKS_PER_GPU = 2
gpu_semaphores = {
    "0": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
    "1": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
}

# 格式化时间函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    return seconds

# 设置一个环境变量来控制调试模式
DEBUG = os.environ.get('DEBUG', 'false').lower() == 'true'

# 转录音频文件函数
def transcribe_audio(audio_filepath, task, return_timestamps, progress=gr.Progress()):
    global current_task
    task_monitor = TaskMonitor()
    
    try:
        task_monitor.start()
        current_task = {
            'start_time': time.time(),
            'filepath': audio_filepath,
            'monitor': task_monitor
        }
        
        if audio_filepath is None:
            logger.warning("未提交音频文件")
            raise gr.Error("未提交音频文件！请上传音频文件后再提交请求。")

        file_size_mb = os.stat(audio_filepath).st_size / (1024 * 1024)
        if file_size_mb > FILE_LIMIT_MB:
            logger.warning("文件大小超过限制")
            raise gr.Error(
                f"文件大小超过限制。当前文件大小为 {file_size_mb:.2f}MB，限制为 {FILE_LIMIT_MB}MB。"
            )

        with gpu_lock:
            current_gpu = GPUS[next_gpu]
            next_gpu = (next_gpu + 1) % len(GPUS)

        gpu_semaphore = gpu_semaphores[current_gpu]
        logger.info(f"等待 GPU {current_gpu} 的信号量...")
        gpu_semaphore.acquire()
        logger.info(f"已获取 GPU {current_gpu} 的信号量")

        try:
            env = os.environ.copy()
            env["CUDA_VISIBLE_DEVICES"] = current_gpu
            
            process = subprocess.Popen(
                [
                    "python", TRANSCRIBE_WORKER_PATH,
                    audio_filepath,
                    task,
                    str(return_timestamps)
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )

            # 设置超时时间为5分钟
            timeout = 300  # 5分钟 = 300秒
            start_time = time.time()
            output_lines = []
            error_lines = []
            process_completed = False

            try:
                while True:
                    # 检查进程是否已经结束
                    if process.poll() is not None:
                        process_completed = True
                        break

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        logger.warning(f"进程 {process.pid} 执行超时（{timeout}秒），正在终止...")
                        process.terminate()
                        try:
                            # 等待2秒让进程正常结束
                            process.wait(2)
                        except subprocess.TimeoutExpired:
                            # 如果2秒后还没结束，强制结束
                            process.kill()
                            logger.warning(f"强制终止进程 {process.pid}")
                        raise gr.Error("转录任务超时。请尝试处理更短的音频文件或检查系统资源使用情况。")

                    # 非阻塞方式读取输出
                    output_line = process.stdout.readline()
                    if output_line:
                        output_lines.append(output_line)
                        if output_line.startswith("progress:"):
                            try:
                                progress_value = float(output_line.strip().split(":")[1])
                                progress(progress_value)
                            except (ValueError, IndexError):
                                pass
                        continue

                    error_line = process.stderr.readline()
                    if error_line:
                        error_lines.append(error_line)
                        continue

                    # 如果没有新的输出，短暂休眠以避免CPU过度使用
                    if not output_line and not error_line:
                        time.sleep(0.1)

                # 读取剩余的输出
                remaining_output, remaining_error = process.communicate()
                if remaining_output:
                    output_lines.append(remaining_output)
                if remaining_error:
                    error_lines.append(remaining_error)

                if process.returncode != 0:
                    error_message = "".join(error_lines)
                    logger.error(f"转录失败: {error_message}")
                    raise gr.Error(f"转录失败: {error_message}")

                # 解析输出结果
                output_text = "".join(output_lines)
                try:
                    # 查找最后一个有效的JSON输出
                    json_outputs = [line for line in output_text.split('\n') if line.strip().startswith('{')]
                    if not json_outputs:
                        raise ValueError("No JSON output found")
                    result = json.loads(json_outputs[-1])
                    return result["text"], result["runtime"]
                except Exception as e:
                    logger.error(f"解析输出失败: {str(e)}")
                    raise gr.Error(f"解析输出失败: {str(e)}")

            finally:
                # 确保在任何情况下都释放GPU信号量
                gpu_semaphore.release()
                logger.info(f"已释放 GPU {current_gpu} 的信号量")
                
                # 确保进程被终止
                if not process_completed:
                    try:
                        process.terminate()
                        process.wait(2)
                    except:
                        process.kill()
        except subprocess.TimeoutExpired:
            logger.error("转录任务超时")
            raise gr.Error("转录任务超时，请稍后再试。")
        except Exception as e:
            logger.error(f"转录过程中发生错误: {str(e)}")
            raise gr.Error(f"转录过程中发生错误: {str(e)}")

    finally:
        if task_monitor:
            task_monitor.stop()
        current_task = None

# 只下载音频文件的函数
def download_yt_audio(yt_url, filename, progress_callback=None):
    logger.info(f"开始下载YouTube视频: {yt_url} -> {filename}")
    ydl_opts = {
        "outtmpl": filename,  # 不包含扩展名
        "format": "bestaudio/best",
        "postprocessors": [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',  # 可选 'wav', 'mp3' 等
            'preferredquality': '192',
        }],
        "postprocessor_args": [
            "-strict", "-2"
        ],
        "prefer_ffmpeg": True,
        "keepvideo": False,  # 不保留视频文件
        "progress_hooks": [progress_callback] if progress_callback else [],
        "proxy": "socks5://192.168.2.3:7891",  # 添加代理设置
        "http_proxy": "socks5://192.168.2.3:7891",
        "https_proxy": "socks5://192.168.2.3:7891",
        "verbose": True,  # 添加详细日志
        "no_warnings": False  # 显示警告信息
    }
    logger.info("YouTube下载选项配置完成")
    with youtube_dl.YoutubeDL(ydl_opts) as ydl:
        try:
            logger.info("开始执行YouTube下载")
            ydl.download([yt_url])
            logger.info("YouTube下载完成")
        except youtube_dl.utils.ExtractorError as err:
            logger.error(f"YouTube提取错误: {str(err)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise gr.Error(str(err))
        except Exception as e:
            logger.error(f"下载失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise gr.Error(f"下载失败: {e}")

# 转录YouTube视频函数
def transcribe_youtube(yt_url, task, return_timestamps, progress=gr.Progress()):
    logger.info(f"开始处理YouTube转录请求: URL={yt_url}, task={task}")
    global next_gpu
    if yt_url is None or yt_url.strip() == "":
        logger.warning("未提交YouTube URL")
        raise gr.Error("未提交YouTube URL！请粘贴YouTube视频的URL后再提交请求。")

    unique_id = uuid.uuid4().hex
    logger.info(f"生成任务ID: {unique_id}")
    # 不包含扩展名，后处理器会添加 .m4a
    filepath = os.path.join(DOWNLOADS_DIR, f"audio_{unique_id}")

    def yt_progress_hook(d):
        if d['status'] == 'downloading':
            total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
            downloaded_bytes = d.get('downloaded_bytes', 0)
            if total_bytes:
                percentage = downloaded_bytes / total_bytes
                speed = d.get('speed')
                if speed is not None and speed > 0:
                    speed_mib_s = speed / (1024 * 1024)
                    speed_str = f"{speed_mib_s:.2f} MiB/s"
                else:
                    speed_str = "未知速度"
                progress(percentage, desc=f"下载中... {percentage*100:.2f}% at {speed_str}")
        elif d['status'] == 'finished':
            progress(1, desc="下载完成。准备转录...")

    try:
        download_yt_audio(yt_url, filepath, progress_callback=yt_progress_hook)
    except gr.Error as e:
        raise e
    except Exception as e:
        logger.error(f"下载失败: {e}")
        raise gr.Error(f"下载失败: {e}")

    # postprocessor 会生成 'audio_{id}.m4a'
    audio_m4a_path = f"{filepath}.m4a"

    with gpu_lock:
        current_gpu = GPUS[next_gpu]
        next_gpu = (next_gpu + 1) % len(GPUS)

    gpu_semaphore = gpu_semaphores[current_gpu]
    logger.info(f"等待 GPU {current_gpu} 的信号量...")
    gpu_semaphore.acquire()
    logger.info(f"已获取 GPU {current_gpu} 的信号量")

    try:
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = current_gpu
        
        process = subprocess.Popen(
            [
                "python", TRANSCRIBE_WORKER_PATH,
                audio_m4a_path,
                task,
                str(return_timestamps)
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        
        # 读取进程输出并更新进度
        output = []
        error_output = []
        last_progress_time = time.time()
        start_time = time.time()
        
        while True:
            if process.poll() is not None:
                break
            
            line = process.stdout.readline()
            if not line:
                break
            
            if line.startswith("progress:"):
                progress_value = float(line.split(":")[1])
                progress(progress_value, desc=f"转录中... {progress_value*100:.1f}%")
            elif line.startswith("{"):  # 假设这是JSON输出
                result = json.loads(line)
                text = result.get("text", "")
                runtime = result.get("runtime", 0)
            else:
                print(f"Worker stdout: {line.strip()}")
        
        # 确保进度达到100%
        progress(1.0, desc="转录完成")
        
        process.wait(timeout=10)
        
        # 合并所有输出行
        full_output = ''.join(output)
        full_error_output = ''.join(error_output)
    
    except subprocess.TimeoutExpired:
        logger.error("转录任务超时")
        raise gr.Error("转录任务超时，请稍后再试。")
    except Exception as e:
        logger.error(f"转录过程中发生错误: {str(e)}")
        raise gr.Error(f"转录过程中发生错误: {str(e)}")
    finally:
        if process.poll() is None:
            process.kill()
        gpu_semaphore.release()
        logger.info(f"已释放 GPU {current_gpu} 的信号量")

    # 在处理完成后
    del process  # 确保进程对象被删除
    gc.collect()  # 触发垃圾回收

    if not text or not runtime:
        raise gr.Error("转录结果无效")

    return runtime, text

# 添加任务超时监控机制
TASK_TIMEOUT = 300  # 5分钟超时
task_start_time = None
current_task = None

class TaskMonitor:
    def __init__(self, timeout=TASK_TIMEOUT):
        self.timeout = timeout
        self.start_time = None
        self.monitoring = False
        self.monitor_thread = None

    def start(self):
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_task, daemon=True)
        self.monitor_thread.start()

    def stop(self):
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def _monitor_task(self):
        while self.monitoring:
            if time.time() - self.start_time > self.timeout:
                logger.warning("主进程任务超时，正在终止程序...")
                # 发送SIGTERM信号给自己（主进程）
                os.kill(os.getpid(), signal.SIGTERM)
                break
            time.sleep(1)

# 添加信号处理函数
def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，正在清理资源...")
    # 释放所有GPU信号量
    for semaphore in gpu_semaphores.values():
        try:
            semaphore.release()
        except:
            pass
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

# 创建Gradio界面
def create_gradio_interface():
    with gr.Blocks(theme=gr.themes.Default(primary_hue=colors.blue, secondary_hue=colors.blue)) as demo:
        gr.Markdown("## Whisper JAX: The Fastest Whisper API ⚡️")
        gr.Markdown("""Whisper JAX是OpenAI Whisper模型的优化实现。它在JAX上运行，后端使用TPU v4-8。""")

        # YouTube 标签页
        with gr.Tab("YouTube"):
            with gr.Column():
                yt_url = gr.Textbox(lines=1, placeholder="在此粘贴YouTube视频的URL", label="YouTube URL")
                task = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn = gr.Button("转录")
                transcription_time = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn.click(
                    fn=transcribe_youtube,
                    inputs=[yt_url, task, return_timestamps],
                    outputs=[transcription_time, transcription],
                    show_progress=True,
                    concurrency_limit=2
                )

        # ���克风 标签页
        with gr.Tab("麦克风"):
            with gr.Column():
                audio_file = gr.Audio(type="filepath", label="音频文件")
                task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_mic = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn_mic = gr.Button("转录")
                transcription_time_mic = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_mic = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn_mic.click(
                    fn=transcribe_audio,
                    inputs=[audio_file, task_mic, return_timestamps_mic],
                    outputs=[transcription_time_mic, transcription_mic],
                    show_progress=True,
                    concurrency_limit=2
                )

        # 音频文件 标签页
        with gr.Tab("音频文件"):
            with gr.Column():
                upload_audio = gr.Audio(label="音频文件", type="filepath")
                task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_upload = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn_upload = gr.Button("转录")
                transcription_time_upload = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_upload = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn_upload.click(
                    fn=transcribe_audio,
                    inputs=[upload_audio, task_upload, return_timestamps_upload],
                    outputs=[transcription_time_upload, transcription_upload],
                    show_progress=True,
                    concurrency_limit=2
                )

        gr.Markdown("Whisper large-v3模型由OpenAI提供。后端通过TRC计划支持的TPU v4-8运行。")
    return demo

def main():
    # 创建Gradio界面
    interface = create_gradio_interface()
    
    # 添加优雅关闭处理
    def graceful_shutdown(*args):
        logger.info("正在关闭服务...")
        # 释放所有GPU信号量
        for semaphore in gpu_semaphores.values():
            try:
                semaphore.release()
            except:
                pass
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGTERM, graceful_shutdown)
    signal.signal(signal.SIGINT, graceful_shutdown)
    
    # 启动服务
    interface.launch(
        server_name="0.0.0.0",
        server_port=7850,
        share=False,
        show_error=True,
        max_threads=MAX_CONCURRENT_TASKS_PER_GPU * len(GPUS)
    )

if __name__ == "__main__":
    main()
