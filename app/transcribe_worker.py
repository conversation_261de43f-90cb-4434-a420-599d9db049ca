# transcribe_worker.py

# 配置日志
import logging
import sys
import json
import os
import time
import numpy as np
from transformers.pipelines.audio_utils import ffmpeg_read
import argparse
import traceback
import gc
import shutil
import tempfile

# 导入原始的OpenAI Whisper模型
import whisper

# 注释掉原来的whisper-jax导入
# import jax
# import jax.numpy as jnp
# from whisper_jax import FlaxWhisperPipline

# 初始化日志器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 初始化控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s;%(levelname)s;%(message)s', '%Y-%m-%d %H:%M:%S')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件处理器将在命令行参数解析后添加

def get_system_info():
    """获取详细的系统信息"""
    import psutil
    import os
    import subprocess
    import platform

    system_info = {}

    # 获取内存信息
    try:
        mem = psutil.virtual_memory()
        system_info['memory'] = {
            'total': mem.total / (1024 * 1024 * 1024),  # GB
            'available': mem.available / (1024 * 1024 * 1024),  # GB
            'used': mem.used / (1024 * 1024 * 1024),  # GB
            'percent': mem.percent
        }
    except Exception as e:
        system_info['memory'] = {'error': str(e)}

    # 获取磁盘信息
    try:
        disk = psutil.disk_usage('/')
        system_info['disk'] = {
            'total': disk.total / (1024 * 1024 * 1024),  # GB
            'free': disk.free / (1024 * 1024 * 1024),  # GB
            'used': disk.used / (1024 * 1024 * 1024),  # GB
            'percent': disk.percent
        }
    except Exception as e:
        system_info['disk'] = {'error': str(e)}

    # 获取CPU信息
    try:
        system_info['cpu'] = {
            'percent': psutil.cpu_percent(interval=0.1),
            'count': psutil.cpu_count(),
            'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    except Exception as e:
        system_info['cpu'] = {'error': str(e)}

    # 获取进程信息
    try:
        process = psutil.Process(os.getpid())
        system_info['process'] = {
            'memory_info': process.memory_info().rss / (1024 * 1024),  # MB
            'cpu_percent': process.cpu_percent(interval=0.1),
            'create_time': process.create_time(),
            'nice': process.nice()
        }
    except Exception as e:
        system_info['process'] = {'error': str(e)}

    # 获取GPU信息
    try:
        system_info['gpu'] = {}

        # 尝试获取NVIDIA GPU信息
        try:
            nvidia_smi = subprocess.check_output(['nvidia-smi', '--query-gpu=name,memory.total,memory.used,memory.free,utilization.gpu', '--format=csv,noheader']).decode('utf-8').strip()
            system_info['gpu']['nvidia_smi'] = nvidia_smi
        except:
            system_info['gpu']['nvidia_smi'] = 'Not available'
    except Exception as e:
        system_info['gpu'] = {'error': str(e)}

    # 获取系统信息
    try:
        system_info['platform'] = {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'python_version': platform.python_version()
        }
    except Exception as e:
        system_info['platform'] = {'error': str(e)}

    # 获取环境变量
    try:
        system_info['env'] = {
            'CUDA_VISIBLE_DEVICES': os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set'),
            'PYTHONPATH': os.environ.get('PYTHONPATH', 'Not set'),
            'LD_LIBRARY_PATH': os.environ.get('LD_LIBRARY_PATH', 'Not set')
        }
    except Exception as e:
        system_info['env'] = {'error': str(e)}

    return system_info

def get_memory_info():
    """获取内存使用情况"""
    try:
        with open('/proc/self/status') as f:
            for line in f:
                if line.startswith('VmRSS:'):
                    ram_used = int(line.split()[1]) / 1024  # Convert KB to MB
                    break
    except:
        ram_used = -1

    # 尝试获取GPU信息
    gpu_info = "Not available"
    try:
        import subprocess
        nvidia_smi = subprocess.check_output(['nvidia-smi', '--query-gpu=memory.used', '--format=csv,noheader']).decode('utf-8').strip()
        gpu_info = nvidia_smi
    except:
        pass

    return {
        'ram_used': ram_used,  # MB
        'gpu_info': gpu_info
    }

def print_json(data):
    """以JSON格式打印数据，确保每条消息都是完整的JSON"""
    try:
        json_str = json.dumps(data, ensure_ascii=False)
        print(json_str, flush=True)
        # 确保输出被立即刷新
        sys.stdout.flush()
    except Exception as e:
        print(f"{{\"type\": \"error\", \"message\": \"JSON序列化错误: {str(e)}\"}}", flush=True)
        sys.stdout.flush()

def print_debug(message, data=None):
    """打印调试信息"""
    debug_info = {
        "type": "debug",
        "timestamp": time.time(),
        "message": message,
        "memory": get_memory_info()
    }
    if data:
        debug_info["data"] = data
    print_json(debug_info)

def format_timestamp(seconds: float, always_include_hours: bool = False):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}"
    else:
        return seconds  # 处理格式错误的时间戳

def traditional_to_simplified(text):
    """将繁体中文转换为简体中文"""
    # 常见繁体字到简体字的映射
    trad_to_simp = {
        '髮': '发', '內': '内', '經': '经', '說': '说', '東': '东', '車': '车',
        '會': '会', '無': '无', '來': '来', '這': '这', '時': '时', '實': '实',
        '國': '国', '產': '产', '後': '后', '為': '为', '樣': '样', '點': '点',
        '學': '学', '體': '体', '們': '们', '對': '对', '業': '业', '電': '电',
        '開': '开', '長': '长', '還': '还', '當': '当', '與': '与', '關': '关',
        '問': '问', '讓': '让', '發': '发', '見': '见', '現': '现', '覺': '觉',
        '聽': '听', '過': '过', '錯': '错', '樂': '乐', '讀': '读', '從': '从',
        '頭': '头', '處': '处', '話': '话', '應': '应', '員': '员', '麼': '么',
        '間': '间', '幾': '几', '總': '总', '萬': '万', '條': '条', '門': '门',
        '週': '周', '歲': '岁', '華': '华', '語': '语', '區': '区', '數': '数',
        '場': '场', '壓': '压', '著': '着', '願': '愿', '難': '难', '風': '风',
        '權': '权', '單': '单', '張': '张', '該': '该', '際': '际', '觀': '观',
        '讚': '赞', '鄉': '乡', '親': '亲', '熱': '热', '兒': '儿', '爾': '尔',
        '隻': '只', '線': '线', '務': '务', '節': '节', '鐵': '铁', '響': '响',
        '連': '连', '遠': '远', '視': '视', '類': '类', '衛': '卫', '衝': '冲',
        '餘': '余', '專': '专', '興': '兴', '執': '执', '創': '创', '勝': '胜',
        '則': '则', '務': '务', '臺': '台', '萬': '万', '黃': '黄', '週': '周',
        '藝': '艺', '陳': '陈', '盡': '尽', '層': '层', '達': '达', '傳': '传',
        '嘗': '尝', '鳥': '鸟', '齊': '齐', '團': '团', '鬥': '斗', '鬆': '松',
        '憶': '忆', '懷': '怀', '濟': '济', '燒': '烧', '為': '为', '產': '产',
        '畫': '画', '盤': '盘', '眾': '众', '碼': '码', '窮': '穷', '練': '练',
        '繼': '继', '續': '续', '蘭': '兰', '蟲': '虫', '親': '亲', '觸': '触',
        '證': '证', '貓': '猫', '貝': '贝', '貨': '货', '貴': '贵', '買': '买',
        '賣': '卖', '賽': '赛', '贊': '赞', '趕': '赶', '輕': '轻', '輪': '轮',
        '轉': '转', '農': '农', '遊': '游', '運': '运', '過': '过', '達': '达',
        '違': '违', '連': '连', '選': '选', '邊': '边', '醫': '医', '鋼': '钢',
        '錢': '钱', '錯': '错', '鐘': '钟', '鑽': '钻', '長': '长', '門': '门',
        '閉': '闭', '開': '开', '閒': '闲', '閱': '阅', '闊': '阔', '闖': '闯',
        '關': '关', '陽': '阳', '階': '阶', '隨': '随', '險': '险', '雖': '虽',
        '電': '电', '靜': '静', '頁': '页', '題': '题', '額': '额', '顏': '颜',
        '願': '愿', '風': '风', '飛': '飞', '飯': '饭', '飽': '饱', '馬': '马',
        '驗': '验', '體': '体', '髮': '发', '鬥': '斗', '鬧': '闹', '魚': '鱼',
        '鳥': '鸟', '麗': '丽', '麥': '麦', '麵': '面', '黃': '黄', '點': '点',
        '鼠': '鼠', '齊': '齐', '齒': '齿', '龍': '龙', '龜': '龟'
    }

    # 使用正则表达式替换所有繁体字
    for trad, simp in trad_to_simp.items():
        text = text.replace(trad, simp)

    return text

def remove_duplicates(text):
    lines = text.split("\n")
    cleaned_lines = []
    for line in lines:
        parts = line.split("] ", 1)
        if len(parts) == 2:
            timestamp, content = parts
            # 移除重复的内容（可能是由于双语输出造成的）
            words = content.split()
            unique_words = []
            for word in words:
                if not unique_words or word.lower() != unique_words[-1].lower():
                    unique_words.append(word)
            cleaned_content = " ".join(unique_words)
            cleaned_lines.append(f"{timestamp}] {cleaned_content}")
        else:
            cleaned_lines.append(line)
    return "\n".join(cleaned_lines)

def main():
    try:
        print_debug("开始运行转录工作进程")
        print_debug("Python版本", {"version": sys.version})
        print_debug("Whisper版本", {"version": whisper.__version__})

        # 输出详细的系统信息
        try:
            system_info = get_system_info()
            print_debug("系统信息", system_info)
        except Exception as e:
            print_debug(f"获取系统信息时出错: {str(e)}")

        # 检查文件权限
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            print_debug("检查文件权限", {
                "current_dir": current_dir,
                "readable": os.access(current_dir, os.R_OK),
                "writable": os.access(current_dir, os.W_OK),
                "executable": os.access(current_dir, os.X_OK),
                "user": os.getuid(),
                "group": os.getgid()
            })
        except Exception as e:
            print_debug(f"检查文件权限时出错: {str(e)}")

        # 检查网络连接
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            print_debug("网络连接检查", {"ip": s.getsockname()[0]})
            s.close()
        except Exception as e:
            print_debug(f"检查网络连接时出错: {str(e)}")

        if len(sys.argv) < 3:
            error_msg = "参数不足，需要：--audio <音频文件> --gpu <GPU ID> --task <任务类型> [--return_timestamps]"
            logger.error(error_msg)
            print_json({"type": "error", "message": error_msg})
            sys.exit(1)

        # 解析命令行参数
        parser = argparse.ArgumentParser(description='Whisper 转录工具')
        parser.add_argument('--audio', type=str, required=True, help='音频文件路径')
        parser.add_argument('--gpu', type=str, required=True, help='GPU ID')
        parser.add_argument('--task', type=str, required=True, help='任务类型')
        parser.add_argument('--return_timestamps', action='store_true', help='是否返回时间戳')
        parser.add_argument('--output_file', type=str, help='输出文件路径，用于存储结果')
        parser.add_argument('--model', type=str, default='base', help='Whisper模型名称 (tiny, base, small, medium, large)')
        parser.add_argument('--language', type=str, help='音频语言代码 (如zh, en)')
        parser.add_argument('--log_file', type=str, help='日志文件路径，用于存储日志')

        args = parser.parse_args()

        # 如果提供了日志文件路径，添加文件日志处理器
        if args.log_file:
            try:
                file_handler = logging.FileHandler(args.log_file, encoding='utf-8')
                file_handler.setLevel(logging.INFO)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                logger.error(f"添加日志文件处理器失败: {str(e)}")

        audio_filepath = args.audio
        task = args.task
        return_timestamps = args.return_timestamps

        # 检查音频文件是否存在
        if not os.path.exists(audio_filepath):
            error_msg = f"音频文件不存在: {audio_filepath}"
            logger.error(error_msg)
            print_json({"type": "error", "message": error_msg})
            sys.exit(1)

        # 设置 GPU
        os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
        print_debug("设置GPU环境变量", {
            "CUDA_VISIBLE_DEVICES": os.environ.get("CUDA_VISIBLE_DEVICES"),
            "XLA_PYTHON_CLIENT_PREALLOCATE": os.environ.get("XLA_PYTHON_CLIENT_PREALLOCATE"),
            "XLA_PYTHON_CLIENT_MEM_FRACTION": os.environ.get("XLA_PYTHON_CLIENT_MEM_FRACTION", "未设置"),
            "XLA_PYTHON_CLIENT_ALLOCATOR": os.environ.get("XLA_PYTHON_CLIENT_ALLOCATOR", "未设置")
        })

        # 创建临时目录用于存储临时文件
        temp_dir = tempfile.mkdtemp()
        print_debug("创建临时目录", {"path": temp_dir})
        temp_files = []  # 记录需要清理的临时文件

        # 准备加载原始Whisper模型
        model_name = args.model  # 使用命令行参数指定的模型
        print_debug("准备加载模型", {"model": model_name})

        # 初始化模型
        print_debug("开始加载模型")
        model = None
        try:
            # 记录初始内存状态
            print_debug("初始化前的内存状态", get_memory_info())

            # 对于large-v3模型，先尝试清理内存
            if model_name == "large-v3":
                print_debug("正在加载large-v3模型，先进行内存清理")
                gc.collect()
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        print_debug("CUDA缓存已清理")
                except ImportError:
                    print_debug("未安装PyTorch，跳过CUDA缓存清理")
                except Exception as e:
                    print_debug(f"清理CUDA缓存时出错: {str(e)}")

            # 加载模型
            start_time = time.time()

            # 发送模型加载开始信息
            print_json({
                "type": "progress",
                "value": 0.15,
                "message": f"开始加载模型: {model_name}",
                "memory_info": get_memory_info()
            })

            # 每2秒发送一次进度更新，避免用户界面卡死
            loading_thread_active = True
            def loading_progress_updater():
                progress_value = 0.15
                while loading_thread_active:
                    progress_value += 0.01
                    if progress_value > 0.3:
                        progress_value = 0.15
                    print_json({
                        "type": "progress",
                        "value": progress_value,
                        "message": f"正在加载模型: {model_name}...",
                        "memory_info": get_memory_info()
                    })
                    time.sleep(2)

            # 启动进度更新线程
            import threading
            loading_thread = threading.Thread(target=loading_progress_updater)
            loading_thread.daemon = True
            loading_thread.start()

            try:
                model = whisper.load_model(model_name)
                # 停止进度更新线程
                loading_thread_active = False
                loading_thread.join(timeout=1)
            except Exception as e:
                # 停止进度更新线程
                loading_thread_active = False
                loading_thread.join(timeout=1)
                raise e

            init_time = time.time() - start_time
            print_debug(f"模型加载成功，用时 {init_time:.2f} 秒")

            print_json({
                "type": "progress",
                "value": 0.3,
                "message": f"模型加载完成，用时 {init_time:.2f} 秒",
                "memory_info": get_memory_info()
            })

            # 记录模型信息
            print_debug("模型信息", {
                "model_type": model.__class__.__name__,
                "model_size": model_name,
                "dimensions": model.dims.__dict__ if hasattr(model, 'dims') else {},
                "language_detection": model.is_multilingual
            })

            # 记录初始化后的内存状态
            print_debug("初始化后的内存状态", get_memory_info())

        except Exception as e:
            error_msg = f"模型加载失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({
                "type": "error",
                "message": error_msg,
                "traceback": traceback.format_exc(),
                "memory_info": get_memory_info()
            })
            sys.exit(1)

        # 读取并转换音频文件
        print_debug(f"开始读取音频文件: {audio_filepath}")
        try:
            # 首先检查文件是否存在和可访问
            if not os.path.exists(audio_filepath):
                error_msg = f"音频文件不存在: {audio_filepath}"
                logger.error(error_msg)
                print_json({"type": "error", "message": error_msg})
                sys.exit(1)

            # 检查文件大小
            file_size = os.path.getsize(audio_filepath)
            if file_size == 0:
                error_msg = f"音频文件大小为0字节: {audio_filepath}"
                logger.error(error_msg)
                print_json({"type": "error", "message": error_msg})
                sys.exit(1)

            print_debug(f"音频文件大小: {file_size} 字节")

            # 读取文件内容
            with open(audio_filepath, "rb") as f:
                inputs_data = f.read()
            print_debug("音频文件读取完成", {"file_size": len(inputs_data)})

            # 检查读取的数据是否有效
            if len(inputs_data) == 0:
                error_msg = "读取到的音频数据为空"
                logger.error(error_msg)
                print_json({"type": "error", "message": error_msg})
                sys.exit(1)

        except Exception as e:
            error_msg = f"无法读取音频文件: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({"type": "error", "message": error_msg, "traceback": traceback.format_exc()})
            sys.exit(1)

        try:
            print_debug("开始音频预处理")

            # 尝试使用 ffmpeg 读取音频
            try:
                print_debug("使用 ffmpeg 读取音频数据")

                # 检查音频数据的前几个字节，判断文件格式
                header_bytes = inputs_data[:16] if len(inputs_data) >= 16 else inputs_data
                header_hex = ' '.join([f'{b:02x}' for b in header_bytes])
                print_debug("音频文件头部字节", {"hex": header_hex})

                # 尝试检测文件格式
                import magic
                try:
                    mime = magic.Magic(mime=True)
                    file_type = mime.from_buffer(inputs_data)
                    print_debug("检测到的文件类型", {"mime": file_type})
                except Exception as e:
                    print_debug(f"无法检测文件类型: {str(e)}")

                # 尝试使用 ffmpeg 读取
                print_debug("开始调用 ffmpeg_read 函数")
                ffmpeg_start_time = time.time()
                # Whisper模型的采样率是16000
                sampling_rate = 16000
                inputs = ffmpeg_read(inputs_data, sampling_rate)
                ffmpeg_time = time.time() - ffmpeg_start_time
                print_debug(f"ffmpeg_read 完成，用时 {ffmpeg_time:.2f} 秒")

                # 检查输出是否有效
                if inputs is None or inputs.size == 0:
                    error_msg = "ffmpeg 转换后的音频数据为空"
                    logger.error(error_msg)
                    print_json({"type": "error", "message": error_msg})
                    sys.exit(1)

                print_debug("音频数据读取成功", {
                    "shape": inputs.shape,
                    "dtype": str(inputs.dtype),
                    "min": float(inputs.min()),
                    "max": float(inputs.max()),
                    "mean": float(inputs.mean()),
                    "std": float(inputs.std()),
                    "has_nan": bool(np.isnan(inputs).any()),
                    "has_inf": bool(np.isinf(inputs).any())
                })

            except Exception as e:
                error_msg = f"ffmpeg 读取音频失败: {str(e)}"
                logger.error(f"{error_msg}\n{traceback.format_exc()}")
                print_json({"type": "error", "message": error_msg, "traceback": traceback.format_exc()})
                sys.exit(1)

            # 准备输入数据
            # Whisper模型的采样率是16000
            sampling_rate = 16000
            print_debug("音频预处理完成", {
                "shape": inputs.shape,
                "sampling_rate": sampling_rate,
                "min": float(inputs.min()),
                "max": float(inputs.max()),
                "mean": float(inputs.mean())
            })

        except Exception as e:
            error_msg = f"音频预处理失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({"type": "error", "message": error_msg, "traceback": traceback.format_exc()})
            sys.exit(1)

        # 准备转录选项
        print_debug("准备转录选项")
        transcribe_options = {
            "task": task,
            # 原始Whisper模型不支持return_timestamps参数
            # 但默认会返回时间戳信息
            # "return_timestamps": return_timestamps
        }

        # 如果指定了语言，添加到选项中
        if args.language:
            transcribe_options["language"] = args.language
            print_debug("指定转录语言", {"language": args.language})

        # 发送初始进度信息
        print_json({
            "type": "info",
            "message": "模型已准备就绪，开始转录音频",
            "memory_info": get_memory_info()
        })

        # 开始转录
        print_debug("开始转录过程")
        start_time = time.time()

        try:
            # 执行转录
            print_debug("正在转录音频", {"audio_path": audio_filepath})
            print_json({
                "type": "progress",
                "value": 0.3,  # 初始进度
                "message": "开始转录音频...",
                "memory_info": get_memory_info()
            })

            # 对于large-v3模型，使用更保守的设置
            if model_name == "large-v3":
                print_debug("使用large-v3模型，采用保守设置进行转录")
                # 对于large-v3模型，我们使用更保守的设置
                # 1. 设置较小的beam_size
                # 2. 禁用fp16加速
                # 3. 使用较小的batch_size
                transcribe_options.update({
                    "beam_size": 3,  # 默认是5
                    "fp16": False,   # 禁用fp16加速
                    "batch_size": 8   # 使用较小的batch_size
                })
                print_debug("large-v3模型转录选项", transcribe_options)

                # 再次清理内存
                gc.collect()
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except Exception:
                    pass

            # 发送模型准备完成的进度信息
            print_json({
                "type": "progress",
                "value": 0.3,
                "message": f"模型准备完成，开始转录...",
                "memory_info": get_memory_info()
            })

            # 设置进度更新函数
            last_update_time = time.time()
            def progress_callback(progress):
                nonlocal last_update_time
                current_time = time.time()
                # 每0.5秒更新一次进度，提供更平滑的体验
                if current_time - last_update_time >= 0.5:
                    progress_value = 0.3 + progress * 0.6  # 从30%到90%
                    print_json({
                        "type": "progress",
                        "value": progress_value,
                        "message": f"转录进度: {progress*100:.1f}%",
                        "memory_info": get_memory_info()
                    })
                    last_update_time = current_time

            # 添加进度回调（如果模型支持）
            if hasattr(model, "transcribe") and "progress_callback" in model.transcribe.__code__.co_varnames:
                transcribe_options["progress_callback"] = progress_callback

            result = model.transcribe(audio_filepath, **transcribe_options)

            print_json({
                "type": "progress",
                "value": 0.9,  # 转录完成，进入后处理
                "message": "转录完成，正在处理结果...",
                "memory_info": get_memory_info()
            })

            # 提取转录文本
            text = result["text"]

            # 将繁体中文转换为简体中文，无论是否需要时间戳
            text = traditional_to_simplified(text)

            if return_timestamps and "segments" in result:
                formatted_text = []
                last_timestamp = -10  # 初始化为-10秒，确保第一个时间戳会被添加
                current_segment_texts = []

                for segment in result["segments"]:
                    start_time = segment["start"]
                    text_segment = segment["text"]

                    # 将繁体中文转换为简体中文
                    text_segment = traditional_to_simplified(text_segment)

                    # 如果与上一个时间戳相差不到10秒，则合并文本
                    if start_time - last_timestamp < 10:
                        current_segment_texts.append(text_segment.strip())
                    else:
                        # 如果有累积的文本，先添加到结果中
                        if current_segment_texts:
                            combined_text = " ".join(current_segment_texts)
                            formatted_text.append(f"[{format_timestamp(last_timestamp)}] {combined_text}")

                        # 开始新的时间段
                        current_segment_texts = [text_segment.strip()]
                        last_timestamp = start_time

                # 添加最后一段文本
                if current_segment_texts:
                    combined_text = " ".join(current_segment_texts)
                    formatted_text.append(f"[{format_timestamp(last_timestamp)}] {combined_text}")

                text = "\n".join(formatted_text)

            # 处理双语输出问题
            text = remove_duplicates(text)
            print_debug("转录完成", {"text_length": len(text)})

        except Exception as e:
            error_msg = f"转录过程中出错: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({
                "type": "error",
                "message": error_msg,
                "traceback": traceback.format_exc(),
                "memory_info": get_memory_info()
            })
            sys.exit(1)

        runtime = time.time() - start_time
        print_debug(f"转录完成，耗时 {runtime:.2f} 秒")

        # 输出最终结果
        print_debug("准备输出最终结果")
        result = {
            "type": "result",
            "text": text,
            "runtime": runtime,
            "memory_info": get_memory_info()
        }

        # 先发送一个完成信息
        print_json({
            "type": "info",
            "message": "转录已完成，正在返回结果"
        })

        # 如果指定了输出文件，将结果写入文件
        if args.output_file:
            try:
                print_debug(f"正在将结果写入文件: {args.output_file}")
                # 先写入一个临时文件，然后重命名，确保原子性
                temp_file = args.output_file + ".tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                    # 强制刷新到磁盘
                    f.flush()
                    os.fsync(f.fileno())

                # 重命名为最终文件
                os.rename(temp_file, args.output_file)

                print_debug(f"结果已成功写入文件: {args.output_file}")

                # 写入一个标记文件表示完成
                with open(args.output_file + ".done", 'w') as f:
                    f.write("done")

            except Exception as e:
                print_debug(f"写入结果文件时出错: {str(e)}")
                # 如果写入文件失败，仍然尝试通过标准输出发送结果

        # 然后发送实际结果
        print_json(result)
        # 再次发送结果，确保被接收
        print_json(result)
        # 强制刷新输出
        sys.stdout.flush()

        # 清理和释放资源
        print_debug("开始清理资源")
        if model is not None:
            # 确保模型被正确释放
            try:
                del model
                # 强制进行垃圾回收
                gc.collect()
                print_debug("模型已删除并触发垃圾回收")

                # 如果有CUDA，清理CUDA缓存
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        print_debug("CUDA缓存已清理")
                except Exception as e:
                    print_debug(f"清理CUDA缓存时出错: {str(e)}")
            except Exception as e:
                print_debug(f"删除模型时出错: {str(e)}")

        # 清理临时文件
        for file_path in temp_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print_debug(f"删除临时文件: {file_path}")
                except Exception as e:
                    print_debug(f"删除临时文件失败: {file_path}, 错误: {str(e)}")

        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print_debug(f"删除临时目录: {temp_dir}")
            except Exception as e:
                print_debug(f"删除临时目录失败: {temp_dir}, 错误: {str(e)}")

        # 再次强制垃圾回收
        gc.collect()
        print_debug("资源清理完成")

        print_debug("转录任务结束")
        sys.exit(0)

    except Exception as e:
        error_msg = f"未预期的错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        print_json({
            "type": "error",
            "message": error_msg,
            "traceback": traceback.format_exc(),
            "memory_info": get_memory_info()
        })
        sys.exit(1)

if __name__ == "__main__":
    main()
