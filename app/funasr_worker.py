# funasr_worker.py

# 配置日志
import logging
import sys
import json
import os
import time
import numpy as np
import argparse
import traceback
import gc
import shutil
import tempfile
import re

# 导入FunASR
try:
    from funasr import AutoModel
except ImportError:
    print("错误：未安装FunASR库，请先安装：pip install funasr")
    sys.exit(1)

# 初始化日志器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 初始化控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s;%(levelname)s;%(message)s', '%Y-%m-%d %H:%M:%S')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件处理器将在命令行参数解析后添加

def get_system_info():
    """获取详细的系统信息"""
    import psutil
    import os
    import subprocess
    import platform

    system_info = {}

    # 获取内存信息
    try:
        mem = psutil.virtual_memory()
        system_info['memory'] = {
            'total': mem.total / (1024 * 1024 * 1024),  # GB
            'available': mem.available / (1024 * 1024 * 1024),  # GB
            'used': mem.used / (1024 * 1024 * 1024),  # GB
            'percent': mem.percent
        }
    except Exception as e:
        system_info['memory'] = {'error': str(e)}

    # 获取磁盘信息
    try:
        disk = psutil.disk_usage('/')
        system_info['disk'] = {
            'total': disk.total / (1024 * 1024 * 1024),  # GB
            'free': disk.free / (1024 * 1024 * 1024),  # GB
            'used': disk.used / (1024 * 1024 * 1024),  # GB
            'percent': disk.percent
        }
    except Exception as e:
        system_info['disk'] = {'error': str(e)}

    # 获取CPU信息
    try:
        system_info['cpu'] = {
            'percent': psutil.cpu_percent(interval=0.1),
            'count': psutil.cpu_count(),
            'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    except Exception as e:
        system_info['cpu'] = {'error': str(e)}

    # 获取进程信息
    try:
        process = psutil.Process(os.getpid())
        system_info['process'] = {
            'memory_info': process.memory_info().rss / (1024 * 1024),  # MB
            'cpu_percent': process.cpu_percent(interval=0.1),
            'create_time': process.create_time(),
            'nice': process.nice()
        }
    except Exception as e:
        system_info['process'] = {'error': str(e)}

    # 获取GPU信息
    try:
        system_info['gpu'] = {}

        # 尝试获取NVIDIA GPU信息
        try:
            nvidia_smi = subprocess.check_output(['nvidia-smi', '--query-gpu=name,memory.total,memory.used,memory.free,utilization.gpu', '--format=csv,noheader']).decode('utf-8').strip()
            system_info['gpu']['nvidia_smi'] = nvidia_smi
        except:
            system_info['gpu']['nvidia_smi'] = 'Not available'
    except Exception as e:
        system_info['gpu'] = {'error': str(e)}

    # 获取系统信息
    try:
        system_info['platform'] = {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'python_version': platform.python_version()
        }
    except Exception as e:
        system_info['platform'] = {'error': str(e)}

    # 获取环境变量
    try:
        system_info['env'] = {
            'CUDA_VISIBLE_DEVICES': os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set'),
            'PYTHONPATH': os.environ.get('PYTHONPATH', 'Not set'),
            'LD_LIBRARY_PATH': os.environ.get('LD_LIBRARY_PATH', 'Not set')
        }
    except Exception as e:
        system_info['env'] = {'error': str(e)}

    return system_info

def get_memory_info():
    """获取内存使用情况"""
    try:
        with open('/proc/self/status') as f:
            for line in f:
                if line.startswith('VmRSS:'):
                    ram_used = int(line.split()[1]) / 1024  # Convert KB to MB
                    break
    except:
        ram_used = -1

    # 尝试获取GPU信息
    gpu_info = "Not available"
    try:
        import subprocess
        nvidia_smi = subprocess.check_output(['nvidia-smi', '--query-gpu=memory.used', '--format=csv,noheader']).decode('utf-8').strip()
        gpu_info = nvidia_smi
    except:
        pass

    return {
        'ram_used': ram_used,  # MB
        'gpu_info': gpu_info
    }

def print_json(data):
    """以JSON格式打印数据，确保每条消息都是完整的JSON"""
    try:
        json_str = json.dumps(data, ensure_ascii=False)
        print(json_str, flush=True)
        # 确保输出被立即刷新
        sys.stdout.flush()
    except Exception as e:
        print(f"{{\"type\": \"error\", \"message\": \"JSON序列化错误: {str(e)}\"}}", flush=True)
        sys.stdout.flush()

def print_debug(message, data=None):
    """打印调试信息"""
    debug_info = {
        "type": "debug",
        "timestamp": time.time(),
        "message": message,
        "memory": get_memory_info()
    }
    if data:
        debug_info["data"] = data
    print_json(debug_info)

def format_timestamp(seconds: float, always_include_hours: bool = False):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}"
    return "00:00"  # 处理格式错误的时间戳

def traditional_to_simplified(text):
    """将繁体中文转换为简体中文"""
    # 常见繁体字到简体字的映射
    trad_to_simp = {
        '髮': '发', '內': '内', '經': '经', '說': '说', '東': '东', '車': '车',
        '會': '会', '無': '无', '來': '来', '這': '这', '時': '时', '實': '实',
        '國': '国', '產': '产', '後': '后', '為': '为', '樣': '样', '點': '点',
        '學': '学', '體': '体', '們': '们', '對': '对', '業': '业', '電': '电',
        '開': '开', '長': '长', '還': '还', '當': '当', '與': '与', '關': '关',
        '問': '问', '讓': '让', '發': '发', '見': '见', '現': '现', '覺': '觉',
        '聽': '听', '過': '过', '錯': '错', '樂': '乐', '讀': '读', '從': '从',
        '頭': '头', '處': '处', '話': '话', '應': '应', '員': '员', '麼': '么',
        '間': '间', '幾': '几', '總': '总', '萬': '万', '條': '条', '門': '门',
        '週': '周', '歲': '岁', '華': '华', '語': '语', '區': '区', '數': '数',
        '場': '场', '壓': '压', '著': '着', '願': '愿', '難': '难', '風': '风',
        '權': '权', '單': '单', '張': '张', '該': '该', '際': '际', '觀': '观',
        '讚': '赞', '鄉': '乡', '親': '亲', '熱': '热', '兒': '儿', '爾': '尔',
        '隻': '只', '線': '线', '務': '务', '節': '节', '鐵': '铁', '響': '响',
        '連': '连', '遠': '远', '視': '视', '類': '类', '衛': '卫', '衝': '冲',
        '餘': '余', '專': '专', '興': '兴', '執': '执', '創': '创', '勝': '胜',
        '則': '则', '務': '务', '臺': '台', '萬': '万', '黃': '黄', '週': '周',
        '藝': '艺', '陳': '陈', '盡': '尽', '層': '层', '達': '达', '傳': '传',
        '嘗': '尝', '鳥': '鸟', '齊': '齐', '團': '团', '鬥': '斗', '鬆': '松',
        '憶': '忆', '懷': '怀', '濟': '济', '燒': '烧', '為': '为', '產': '产',
        '畫': '画', '盤': '盘', '眾': '众', '碼': '码', '窮': '穷', '練': '练',
        '繼': '继', '續': '续', '蘭': '兰', '蟲': '虫', '親': '亲', '觸': '触',
        '證': '证', '貓': '猫', '貝': '贝', '貨': '货', '貴': '贵', '買': '买',
        '賣': '卖', '賽': '赛', '贊': '赞', '趕': '赶', '輕': '轻', '輪': '轮',
        '轉': '转', '農': '农', '遊': '游', '運': '运', '過': '过', '達': '达',
        '違': '违', '連': '连', '選': '选', '邊': '边', '醫': '医', '鋼': '钢',
        '錢': '钱', '錯': '错', '鐘': '钟', '鑽': '钻', '長': '长', '門': '门',
        '閉': '闭', '開': '开', '閒': '闲', '閱': '阅', '闊': '阔', '闖': '闯',
        '關': '关', '陽': '阳', '階': '阶', '隨': '随', '險': '险', '雖': '虽',
        '電': '电', '靜': '静', '頁': '页', '題': '题', '額': '额', '顏': '颜',
        '願': '愿', '風': '风', '飛': '飞', '飯': '饭', '飽': '饱', '馬': '马',
        '驗': '验', '體': '体', '髮': '发', '鬥': '斗', '鬧': '闹', '魚': '鱼',
        '鳥': '鸟', '麗': '丽', '麥': '麦', '麵': '面', '黃': '黄', '點': '点',
        '鼠': '鼠', '齊': '齐', '齒': '齿', '龍': '龙', '龜': '龟'
    }

    # 使用正则表达式替换所有繁体字
    for trad, simp in trad_to_simp.items():
        text = text.replace(trad, simp)

    return text

def remove_duplicates(text):
    lines = text.split("\n")
    cleaned_lines = []
    for line in lines:
        parts = line.split("] ", 1)
        if len(parts) == 2:
            timestamp, content = parts
            # 移除重复的内容（可能是由于双语输出造成的）
            words = content.split()
            unique_words = []
            for word in words:
                if not unique_words or word.lower() != unique_words[-1].lower():
                    unique_words.append(word)
            cleaned_content = " ".join(unique_words)
            cleaned_lines.append(f"{timestamp}] {cleaned_content}")
        else:
            cleaned_lines.append(line)
    return "\n".join(cleaned_lines)

def get_funasr_model_path(model_name):
    """根据模型名称获取FunASR模型路径"""
    # 模型名称到模型ID的映射
    model_map = {
        "funasr-zh-base": "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "funasr-zh-medium": "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "funasr-zh-large": "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    }

    if model_name in model_map:
        return model_map[model_name]
    else:
        # 如果找不到映射，直接返回模型名称
        return model_name

def main():
    try:
        print_debug("开始运行FunASR转录工作进程")

        # 输出详细的系统信息
        try:
            system_info = get_system_info()
            print_debug("系统信息", system_info)
        except Exception as e:
            print_debug(f"获取系统信息时出错: {str(e)}")

        if len(sys.argv) < 3:
            error_msg = "参数不足，需要：--audio <音频文件> --gpu <GPU ID> --task <任务类型> [--return_timestamps]"
            logger.error(error_msg)
            print_json({"type": "error", "message": error_msg})
            sys.exit(1)

        # 解析命令行参数
        parser = argparse.ArgumentParser(description='FunASR 转录工具')
        parser.add_argument('--audio', type=str, required=True, help='音频文件路径')
        parser.add_argument('--gpu', type=str, required=True, help='GPU ID')
        parser.add_argument('--task', type=str, required=True, help='任务类型')
        parser.add_argument('--return_timestamps', action='store_true', help='是否返回时间戳')
        parser.add_argument('--output_file', type=str, help='输出文件路径，用于存储结果')
        parser.add_argument('--model', type=str, default='funasr-zh-base', help='FunASR模型名称')
        parser.add_argument('--language', type=str, help='音频语言代码 (如zh, en)')
        parser.add_argument('--log_file', type=str, help='日志文件路径，用于存储日志')

        args = parser.parse_args()

        # 如果提供了日志文件路径，添加文件日志处理器
        if args.log_file:
            try:
                file_handler = logging.FileHandler(args.log_file, encoding='utf-8')
                file_handler.setLevel(logging.INFO)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                logger.error(f"添加日志文件处理器失败: {str(e)}")

        audio_filepath = args.audio
        task = args.task
        return_timestamps = args.return_timestamps
        model_name = args.model

        # 检查音频文件是否存在
        if not os.path.exists(audio_filepath):
            error_msg = f"音频文件不存在: {audio_filepath}"
            logger.error(error_msg)
            print_json({"type": "error", "message": error_msg})
            sys.exit(1)

        # 设置 GPU
        os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
        print_debug("设置GPU环境变量", {
            "CUDA_VISIBLE_DEVICES": os.environ.get("CUDA_VISIBLE_DEVICES"),
        })

        # 创建临时目录用于存储临时文件
        temp_dir = tempfile.mkdtemp()
        print_debug("创建临时目录", {"path": temp_dir})
        temp_files = []  # 记录需要清理的临时文件

        # 获取FunASR模型路径
        model_path = get_funasr_model_path(model_name)
        print_debug("准备加载FunASR模型", {"model": model_name, "path": model_path})

        # 初始化模型
        print_debug("开始加载模型")
        model = None
        try:
            # 记录初始内存状态
            print_debug("初始化前的内存状态", get_memory_info())

            # 清理内存
            gc.collect()
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    print_debug("CUDA缓存已清理")
            except ImportError:
                print_debug("未安装PyTorch，跳过CUDA缓存清理")
            except Exception as e:
                print_debug(f"清理CUDA缓存时出错: {str(e)}")

            # 加载模型
            start_time = time.time()

            # 使用正确的模型加载方式
            try:
                # 先尝试使用modelscope注册模型
                from modelscope.hub.snapshot_download import snapshot_download
                from modelscope.utils.constant import ModelFile

                # 下载模型
                print_debug(f"使用modelscope下载模型: {model_path}")
                model_dir = snapshot_download(model_path, revision='v1.0.4')
                print_debug(f"模型下载到: {model_dir}")

                # 使用本地模型路径创建AutoModel
                model = AutoModel(model=model_dir)
            except Exception as e:
                print_debug(f"modelscope下载失败: {str(e)}")
                # 如果失败，尝试直接使用AutoModel
                model = AutoModel(model=model_path, model_revision="v1.0.4")

            init_time = time.time() - start_time
            print_debug(f"模型加载成功，用时 {init_time:.2f} 秒")

            # 记录初始化后的内存状态
            print_debug("初始化后的内存状态", get_memory_info())

        except Exception as e:
            error_msg = f"模型加载失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({
                "type": "error",
                "message": error_msg,
                "traceback": traceback.format_exc(),
                "memory_info": get_memory_info()
            })
            sys.exit(1)

        # 发送初始进度信息
        print_json({
            "type": "info",
            "message": "模型已准备就绪，开始转录音频",
            "memory_info": get_memory_info()
        })

        # 开始转录
        print_debug("开始转录过程")
        start_time = time.time()

        try:
            # 执行转录
            print_debug("正在转录音频", {"audio_path": audio_filepath})
            print_json({
                "type": "progress",
                "value": 0.1,  # 初始进度
                "memory_info": get_memory_info()
            })

            # 转录音频
            result = model.generate(input=audio_filepath)

            print_json({
                "type": "progress",
                "value": 0.9,  # 转录完成，进入后处理
                "memory_info": get_memory_info()
            })

            # 提取转录文本
            if isinstance(result, dict):
                text = result.get("text", "")
            else:
                text = str(result)

            # 将繁体中文转换为简体中文
            text = traditional_to_simplified(text)

            # 如果需要时间戳，处理时间戳格式
            if return_timestamps and "sentence_info" in result:
                formatted_text = []
                last_timestamp = -10  # 初始化为-10秒，确保第一个时间戳会被添加
                current_segment_texts = []

                for sentence in result["sentence_info"]:
                    start_time = sentence.get("start_time", 0)
                    text_segment = sentence.get("text", "")
                    # 将繁体中文转换为简体中文
                    text_segment = traditional_to_simplified(text_segment)

                    # 如果与上一个时间戳相差不到10秒，则合并文本
                    if start_time - last_timestamp < 10:
                        current_segment_texts.append(text_segment.strip())
                    else:
                        # 如果有累积的文本，先添加到结果中
                        if current_segment_texts:
                            combined_text = " ".join(current_segment_texts)
                            formatted_text.append(f"[{format_timestamp(last_timestamp)}] {combined_text}")

                        # 开始新的时间段
                        current_segment_texts = [text_segment.strip()]
                        last_timestamp = start_time

                # 添加最后一段文本
                if current_segment_texts:
                    combined_text = " ".join(current_segment_texts)
                    formatted_text.append(f"[{format_timestamp(last_timestamp)}] {combined_text}")

                text = "\n".join(formatted_text)
            elif return_timestamps and "timestamped_text" in result:
                # 某些FunASR模型可能使用不同的格式返回时间戳
                text = result["timestamped_text"]

            print_debug("转录完成", {"text_length": len(text)})

        except Exception as e:
            error_msg = f"转录过程中出错: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            print_json({
                "type": "error",
                "message": error_msg,
                "traceback": traceback.format_exc(),
                "memory_info": get_memory_info()
            })
            sys.exit(1)

        runtime = time.time() - start_time
        print_debug(f"转录完成，耗时 {runtime:.2f} 秒")

        # 输出最终结果
        print_debug("准备输出最终结果")
        result = {
            "type": "result",
            "text": text,
            "runtime": runtime,
            "memory_info": get_memory_info()
        }

        # 先发送一个完成信息
        print_json({
            "type": "info",
            "message": "转录已完成，正在返回结果"
        })

        # 如果指定了输出文件，将结果写入文件
        if args.output_file:
            try:
                print_debug(f"正在将结果写入文件: {args.output_file}")
                # 先写入一个临时文件，然后重命名，确保原子性
                temp_file = args.output_file + ".tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                    # 强制刷新到磁盘
                    f.flush()
                    os.fsync(f.fileno())

                # 重命名为最终文件
                os.rename(temp_file, args.output_file)

                print_debug(f"结果已成功写入文件: {args.output_file}")

                # 写入一个标记文件表示完成
                with open(args.output_file + ".done", 'w') as f:
                    f.write("done")

            except Exception as e:
                print_debug(f"写入结果文件时出错: {str(e)}")
                # 如果写入文件失败，仍然尝试通过标准输出发送结果

        # 然后发送实际结果
        print_json(result)
        # 再次发送结果，确保被接收
        print_json(result)
        # 强制刷新输出
        sys.stdout.flush()

        # 清理和释放资源
        print_debug("开始清理资源")
        if model is not None:
            # 确保模型被正确释放
            try:
                del model
                # 强制进行垃圾回收
                gc.collect()
                print_debug("模型已删除并触发垃圾回收")

                # 如果有CUDA，清理CUDA缓存
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        print_debug("CUDA缓存已清理")
                except Exception as e:
                    print_debug(f"清理CUDA缓存时出错: {str(e)}")
            except Exception as e:
                print_debug(f"删除模型时出错: {str(e)}")

        # 清理临时文件
        for file_path in temp_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print_debug(f"删除临时文件: {file_path}")
                except Exception as e:
                    print_debug(f"删除临时文件失败: {file_path}, 错误: {str(e)}")

        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print_debug(f"删除临时目录: {temp_dir}")
            except Exception as e:
                print_debug(f"删除临时目录失败: {temp_dir}, 错误: {str(e)}")

        # 再次强制垃圾回收
        gc.collect()
        print_debug("资源清理完成")

        print_debug("转录任务结束")
        sys.exit(0)

    except Exception as e:
        error_msg = f"未预期的错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        print_json({
            "type": "error",
            "message": error_msg,
            "traceback": traceback.format_exc(),
            "memory_info": get_memory_info()
        })
        sys.exit(1)

if __name__ == "__main__":
    main()
