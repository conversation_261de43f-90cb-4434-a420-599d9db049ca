#!/usr/bin/env python3
"""
并发转录测试脚本
用于测试两个GPU是否能同时处理转录任务
"""

import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 服务器配置
SERVER_URL = "http://localhost:7850"
API_ENDPOINT = f"{SERVER_URL}/api/predict"

def test_transcription(task_id, test_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ"):
    """
    测试单个转录任务
    """
    print(f"[任务 {task_id}] 开始测试转录...")
    
    # 准备请求数据
    data = {
        "data": [
            test_url,      # YouTube URL
            "transcribe",  # 任务类型
            False,         # 返回时间戳
            ["medium - Whisper大型模型，准确性高，速度较慢", "medium"]  # 模型选择
        ],
        "fn_index": 0  # 对应YouTube转录功能
    }
    
    start_time = time.time()
    
    try:
        # 发送请求
        print(f"[任务 {task_id}] 发送转录请求...")
        response = requests.post(API_ENDPOINT, json=data, timeout=600)
        
        if response.status_code == 200:
            result = response.json()
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"[任务 {task_id}] 转录完成！用时: {duration:.2f}秒")
            
            # 提取转录结果
            if "data" in result and len(result["data"]) >= 2:
                transcription_time = result["data"][0]
                transcription_text = result["data"][1]
                print(f"[任务 {task_id}] 转录时间: {transcription_time}秒")
                print(f"[任务 {task_id}] 转录文本长度: {len(transcription_text)}字符")
            
            return {
                "task_id": task_id,
                "success": True,
                "duration": duration,
                "result": result
            }
        else:
            print(f"[任务 {task_id}] 请求失败，状态码: {response.status_code}")
            return {
                "task_id": task_id,
                "success": False,
                "error": f"HTTP {response.status_code}",
                "duration": time.time() - start_time
            }
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"[任务 {task_id}] 发生错误: {str(e)}")
        return {
            "task_id": task_id,
            "success": False,
            "error": str(e),
            "duration": duration
        }

def test_concurrent_transcription(num_tasks=2):
    """
    测试并发转录
    """
    print(f"开始并发转录测试，任务数量: {num_tasks}")
    print("=" * 50)
    
    # 使用线程池执行并发任务
    with ThreadPoolExecutor(max_workers=num_tasks) as executor:
        # 提交所有任务
        futures = []
        for i in range(num_tasks):
            future = executor.submit(test_transcription, i + 1)
            futures.append(future)
        
        # 等待所有任务完成
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    # 分析结果
    print("\n" + "=" * 50)
    print("测试结果分析:")
    print("=" * 50)
    
    successful_tasks = [r for r in results if r["success"]]
    failed_tasks = [r for r in results if not r["success"]]
    
    print(f"成功任务数: {len(successful_tasks)}")
    print(f"失败任务数: {len(failed_tasks)}")
    
    if successful_tasks:
        durations = [r["duration"] for r in successful_tasks]
        avg_duration = sum(durations) / len(durations)
        min_duration = min(durations)
        max_duration = max(durations)
        
        print(f"平均用时: {avg_duration:.2f}秒")
        print(f"最短用时: {min_duration:.2f}秒")
        print(f"最长用时: {max_duration:.2f}秒")
        
        # 检查是否真正并发
        if len(successful_tasks) >= 2:
            time_diff = abs(durations[0] - durations[1])
            if time_diff < 30:  # 如果两个任务的完成时间相差不到30秒，认为是并发的
                print("✅ 任务似乎是并发执行的")
            else:
                print("❌ 任务可能是串行执行的")
    
    if failed_tasks:
        print("\n失败任务详情:")
        for task in failed_tasks:
            print(f"  任务 {task['task_id']}: {task['error']}")
    
    return results

def check_server_status():
    """
    检查服务器状态
    """
    try:
        response = requests.get(SERVER_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False

def main():
    print("Whisper 并发转录测试工具")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        print("请确保Whisper服务正在运行在 http://localhost:7850")
        return
    
    # 执行并发测试
    print("\n开始并发测试...")
    results = test_concurrent_transcription(num_tasks=2)
    
    print("\n测试完成！")
    
    # 保存结果到文件
    with open("concurrent_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print("测试结果已保存到 concurrent_test_results.json")

if __name__ == "__main__":
    main()
