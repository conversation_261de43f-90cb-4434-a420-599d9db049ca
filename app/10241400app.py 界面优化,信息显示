# 导入所需的库
import logging
import os
import uuid
import subprocess
import json
import threading
import gradio as gr
import yt_dlp as youtube_dl  # 使用 yt_dlp 替代 youtube_dl
import time
import gc
from gradio.themes.utils import colors

# 设置工程目录和下载目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 设置transcribe_worker.py的路径
TRANSCRIBE_WORKER_PATH = os.path.join(CURRENT_DIR, "transcribe_worker.py")

# 配置参数
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 14400

# 日志配置
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# GPU与信号量配置
gpu_lock = threading.Lock()
next_gpu = 0
GPUS = ["0", "1"]
MAX_CONCURRENT_TASKS_PER_GPU = 2
gpu_semaphores = {
    "0": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
    "1": threading.Semaphore(MAX_CONCURRENT_TASKS_PER_GPU),
}

# 格式化时间函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    return seconds

# 转录音频文件函数
def transcribe_audio(audio_filepath, task, return_timestamps, progress=gr.Progress()):
    global next_gpu
    if audio_filepath is None:
        logger.warning("未提交音频文件")
        raise gr.Error("未提交音频文件！请上传音频文件后再提交请求。")

    file_size_mb = os.stat(audio_filepath).st_size / (1024 * 1024)
    if file_size_mb > FILE_LIMIT_MB:
        logger.warning("文件大小超过限制")
        raise gr.Error(
            f"文件大小超过限制。当前文件大小为 {file_size_mb:.2f}MB，限制为 {FILE_LIMIT_MB}MB。"
        )

    with gpu_lock:
        current_gpu = GPUS[next_gpu]
        next_gpu = (next_gpu + 1) % len(GPUS)

    gpu_semaphore = gpu_semaphores[current_gpu]
    logger.info(f"等待 GPU {current_gpu} 的信号量...")
    gpu_semaphore.acquire()
    logger.info(f"已获取 GPU {current_gpu} 的信号量")

    try:
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = current_gpu
        
        process = subprocess.Popen(
            [
                "python", TRANSCRIBE_WORKER_PATH,
                audio_filepath,
                task,
                str(return_timestamps)
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        
        # 读取进程输出并更新进度
        output = []
        while True:
            line = process.stdout.readline()
            if line == '' and process.poll() is not None:
                break
            if line:
                output.append(line)
                if line.startswith("progress:"):
                    progress_value = float(line.split(":")[1])
                    progress(progress_value, desc="转录中...")
                else:
                    print(f"Worker stdout: {line.strip()}")  # 打印非进度信息
        
        process.wait(timeout=600)
        
        # 合并所有输出行
        full_output = ''.join(output)
    except subprocess.TimeoutExpired:
        logger.error("转录任务超时")
        raise gr.Error("转录任务超时，请稍后再试。")
    finally:
        gpu_semaphore.release()
        logger.info(f"已释放 GPU {current_gpu} 的信号量")

    if process.returncode != 0:
        error_output = process.stderr.read()
        logger.error(f"转录错误: {error_output}")
        raise gr.Error(f"转录错误: {error_output}")

    try:
        # 尝试解析最后一行作为 JSON
        json_output = full_output.strip().split('\n')[-1]
        result = json.loads(json_output)
        if "error" in result:
            logger.error(f"转录错误: {result['error']}")
            raise gr.Error(f"转录错误: {result['error']}")
        return result["text"], result["runtime"]
    except json.JSONDecodeError:
        logger.error(f"无法解析转录结果: {full_output}")
        raise gr.Error("无法解析转录结果。请检查日志以获取更多信息。")

# 只下载音频文件的函数
def download_yt_audio(yt_url, filename, progress_callback=None):
    ydl_opts = {
        "outtmpl": filename,  # 不包含扩展名
        "format": "bestaudio/best",
        "postprocessors": [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',  # 可选 'wav', 'mp3' 等
            'preferredquality': '192',
        }],
        "postprocessor_args": [
            "-strict", "-2"
        ],
        "prefer_ffmpeg": True,
        "keepvideo": False,  # 不保留视频文件
        "progress_hooks": [progress_callback] if progress_callback else [],
    }
    with youtube_dl.YoutubeDL(ydl_opts) as ydl:
        try:
            ydl.download([yt_url])
        except youtube_dl.utils.ExtractorError as err:
            raise gr.Error(str(err))
        except Exception as e:
            raise gr.Error(f"下载失败: {e}")

# 转录YouTube视频函数
def transcribe_youtube(yt_url, task, return_timestamps, progress=gr.Progress()):
    global next_gpu
    if yt_url is None or yt_url.strip() == "":
        logger.warning("未提交YouTube URL")
        raise gr.Error("未提交YouTube URL！请粘贴YouTube视频的URL后再提交请求。")

    unique_id = uuid.uuid4().hex
    # 不包含扩展名，后处理器会添加 .m4a
    filepath = os.path.join(DOWNLOADS_DIR, f"audio_{unique_id}")

    def yt_progress_hook(d):
        if d['status'] == 'downloading':
            total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
            downloaded_bytes = d.get('downloaded_bytes', 0)
            if total_bytes:
                percentage = downloaded_bytes / total_bytes
                speed = d.get('speed')
                if speed is not None and speed > 0:
                    speed_mib_s = speed / (1024 * 1024)
                    speed_str = f"{speed_mib_s:.2f} MiB/s"
                else:
                    speed_str = "未知速度"
                progress(percentage, desc=f"下载中... {percentage*100:.2f}% at {speed_str}")
        elif d['status'] == 'finished':
            progress(1, desc="下载完成。准备转录...")

    try:
        download_yt_audio(yt_url, filepath, progress_callback=yt_progress_hook)
    except gr.Error as e:
        raise e
    except Exception as e:
        logger.error(f"下载失败: {e}")
        raise gr.Error(f"下载失败: {e}")

    # postprocessor 会生成 'audio_{id}.m4a'
    audio_m4a_path = f"{filepath}.m4a"

    with gpu_lock:
        current_gpu = GPUS[next_gpu]
        next_gpu = (next_gpu + 1) % len(GPUS)

    gpu_semaphore = gpu_semaphores[current_gpu]
    logger.info(f"等待 GPU {current_gpu} 的信号量...")
    gpu_semaphore.acquire()
    logger.info(f"已获取 GPU {current_gpu} 的信号量")

    try:
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = current_gpu
        
        process = subprocess.Popen(
            [
                "python", TRANSCRIBE_WORKER_PATH,
                audio_m4a_path,
                task,
                str(return_timestamps)
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        
        # 读取进程输出并更新进度
        output = []
        error_output = []
        while True:
            line = process.stdout.readline()
            error_line = process.stderr.readline()
            if line == '' and error_line == '' and process.poll() is not None:
                break
            if line:
                output.append(line)
                if line.startswith("progress:"):
                    progress_value = float(line.split(":")[1])
                    progress(progress_value, desc=f"转录中... {progress_value*100:.1f}%")
                else:
                    print(f"Worker stdout: {line.strip()}")  # 打印非进度信息
            if error_line:
                error_output.append(error_line)
                print(f"Worker stderr: {error_line.strip()}")  # 打印错误信息
        
        process.wait(timeout=600)
        
        # 合并所有输出行
        full_output = ''.join(output)
        full_error_output = ''.join(error_output)
    except subprocess.TimeoutExpired:
        logger.error("转录任务超时")
        raise gr.Error("转录任务超时，请稍后再试。")
    finally:
        gpu_semaphore.release()
        logger.info(f"已释放 GPU {current_gpu} 的信号量")

    if process.returncode != 0:
        logger.error(f"转录错误: {full_error_output}")
        raise gr.Error(f"转录错误: {full_error_output}")

    try:
        # 找到最后一个有效的 JSON 对象
        json_output = full_output.strip().split('\n')[-1]
        result = json.loads(json_output)
        if "error" in result:
            logger.error(f"转录错误: {result['error']}")
            raise gr.Error(f"转录错误: {result['error']}")
        text = result["text"]
        runtime = result["runtime"]
    except json.JSONDecodeError:
        logger.error(f"无法解析转录结果: {full_output}")
        raise gr.Error("无法解析转录结果。请检查日志以获取更多信息。")

    # 删除下载的音频文件以节省空间
    try:
        os.remove(audio_m4a_path)
        logger.info(f"已删除临时文件: {audio_m4a_path}")
    except OSError as e:
        logger.warning(f"无法删除临时文件 {audio_m4a_path}: {e}")

    # 在处理完成后
    del process  # 确保进程对象被删除
    gc.collect()  # 触发垃圾回收

    return runtime, text

# 创建Gradio界面
def create_gradio_interface():
    with gr.Blocks(theme=gr.themes.Default(primary_hue=colors.blue, secondary_hue=colors.blue)) as demo:
        gr.Markdown("## Whisper JAX: The Fastest Whisper API ⚡️")
        gr.Markdown("""Whisper JAX是OpenAI Whisper模型的优化实现。它在JAX上运行，后端使用TPU v4-8。""")

        # YouTube 标签页
        with gr.Tab("YouTube"):
            with gr.Column():
                yt_url = gr.Textbox(lines=1, placeholder="在此粘贴YouTube视频的URL", label="YouTube URL")
                task = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn = gr.Button("转录")
                transcription_time = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn.click(
                    fn=transcribe_youtube,
                    inputs=[yt_url, task, return_timestamps],
                    outputs=[transcription_time, transcription],
                    show_progress=True,
                    concurrency_limit=2
                )

        # 麦克风 标签页
        with gr.Tab("麦克风"):
            with gr.Column():
                audio_file = gr.Audio(type="filepath", label="音频文件")
                task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_mic = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn_mic = gr.Button("转录")
                transcription_time_mic = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_mic = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn_mic.click(
                    fn=transcribe_audio,
                    inputs=[audio_file, task_mic, return_timestamps_mic],
                    outputs=[transcription_time_mic, transcription_mic],
                    show_progress=True,
                    concurrency_limit=2
                )

        # 音频文件 标签页
        with gr.Tab("音频文件"):
            with gr.Column():
                upload_audio = gr.Audio(label="音频文件", type="filepath")
                task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_upload = gr.Checkbox(value=True, label="返回时间戳")
                transcribe_btn_upload = gr.Button("转录")
                transcription_time_upload = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_upload = gr.Textbox(label="转录内容", lines=30, show_copy_button=True)
                transcribe_btn_upload.click(
                    fn=transcribe_audio,
                    inputs=[upload_audio, task_upload, return_timestamps_upload],
                    outputs=[transcription_time_upload, transcription_upload],
                    show_progress=True,
                    concurrency_limit=2
                )

        gr.Markdown("Whisper large-v3模型由OpenAI提供。后端通过TRC计划支持的TPU v4-8运行。")
    return demo

def main():
    demo = create_gradio_interface()
    demo.queue(max_size=10)
    demo.launch(server_name="0.0.0.0", show_api=False, max_threads=8)

if __name__ == "__main__":
    main()
