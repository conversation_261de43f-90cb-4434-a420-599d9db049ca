# Whisper 并发转录修复说明

## 问题描述
用户报告当同时发送两个转录任务时，第二个任务需要排队，即使有两个GPU且设置了并行任务数为2，也无法同时运行两个任务。

## 问题原因分析
通过代码分析，发现了以下几个关键问题：

1. **GPU管理器的acquire_gpu方法缺少锁保护**：在`transcribe_audio`函数中，GPU分配没有正确使用锁机制
2. **Gradio队列配置不完整**：缺少`queue()`调用的并发配置参数
3. **GPU分配逻辑不一致**：不同函数中的GPU管理逻辑存在差异

## 修复内容

### 1. 修复GPU管理器 (app.py 第137-163行)
```python
class GPUManager:
    def __init__(self):
        self.gpu_states = {gpu: {"in_use": False, "last_used": 0} for gpu in GPUS}
        self.lock = threading.Lock()

    def acquire_gpu(self):
        with self.lock:  # 添加锁保护
            # 按优先级顺序查找可用的GPU
            for gpu in GPUS:
                if not self.gpu_states[gpu]["in_use"]:
                    self.gpu_states[gpu]["in_use"] = True
                    self.gpu_states[gpu]["last_used"] = time.time()
                    logger.info(f"GPU {gpu} 已被分配")
                    return gpu
            logger.warning("所有GPU都在使用中")
            return None

    def release_gpu(self, gpu):
        with self.lock:
            if gpu in self.gpu_states:
                self.gpu_states[gpu]["in_use"] = False
                logger.info(f"GPU {gpu} 已被释放")

    def get_gpu_status(self):
        """获取GPU使用状态"""
        with self.lock:
            return {gpu: state["in_use"] for gpu, state in self.gpu_states.items()}
```

### 2. 统一GPU分配逻辑
- **transcribe_audio函数**：使用`gpu_manager.acquire_gpu()`替代手动分配
- **transcribe_youtube函数**：移除多余的锁操作，直接使用GPU管理器

### 3. 优化Gradio队列配置 (app.py 第1594-1604行)
```python
interface.queue(
    max_size=20,  # 队列最大大小
    concurrency_count=MAX_CONCURRENT_TASKS,  # 并发数量
    default_concurrency_limit=MAX_CONCURRENT_TASKS  # 默认并发限制
).launch(
    server_name="0.0.0.0",
    server_port=7850,
    share=False,
    show_error=True,
    max_threads=MAX_CONCURRENT_TASKS * 2,  # 增加线程池大小
    prevent_thread_lock=True
)
```

### 4. 添加并发限制到UI组件
为所有转录按钮添加`concurrency_limit=MAX_CONCURRENT_TASKS`参数：
- YouTube转录
- 麦克风转录  
- 音频文件转录

### 5. 添加GPU状态监控功能
在设置页面添加了GPU状态监控，可以实时查看GPU使用情况：
- 显示每个GPU的使用状态（🟢空闲 / 🔴使用中）
- 显示最大并发任务数
- 提供刷新按钮

## 配置参数

### 关键配置变量
```python
GPUS = ["0", "1"]  # 可用的GPU列表
MAX_CONCURRENT_TASKS = 2  # 最大并发任务数
```

### 建议配置
- **双GPU系统**：`GPUS = ["0", "1"]`, `MAX_CONCURRENT_TASKS = 2`
- **单GPU系统**：`GPUS = ["0"]`, `MAX_CONCURRENT_TASKS = 1`
- **四GPU系统**：`GPUS = ["0", "1", "2", "3"]`, `MAX_CONCURRENT_TASKS = 4`

## 测试验证

### 1. GPU管理器测试
运行测试脚本验证GPU分配功能：
```bash
cd /home/<USER>/whisper-jax/app
python test_gpu_manager.py
```

### 2. 并发转录测试
使用测试脚本验证实际转录并发：
```bash
python test_concurrent.py
```

## 预期效果

修复后，系统应该能够：

1. ✅ **同时处理2个转录任务**：当有2个GPU可用时
2. ✅ **正确排队第3个任务**：当所有GPU都被占用时
3. ✅ **自动释放GPU资源**：任务完成后立即释放GPU
4. ✅ **线程安全操作**：多个并发请求不会导致资源冲突
5. ✅ **状态监控**：可以实时查看GPU使用状态

## 故障排除

### 如果仍然无法并发：
1. 检查GPU配置：确认`GPUS`列表正确
2. 检查并发限制：确认`MAX_CONCURRENT_TASKS`设置正确
3. 查看日志：检查GPU分配和释放的日志信息
4. 使用GPU状态监控：在设置页面查看实时GPU状态

### 常见问题：
- **任务仍然串行**：检查Gradio版本，确保支持`concurrency_limit`参数
- **GPU未释放**：检查转录进程是否正常结束，查看异常日志
- **内存不足**：考虑使用较小的模型或减少并发数量

## 性能建议

1. **模型选择**：对于并发场景，建议使用`medium`或更小的模型
2. **内存管理**：监控GPU内存使用，避免内存溢出
3. **任务调度**：合理安排转录任务，避免长时间占用GPU

修复完成后，您的Whisper转录服务应该能够充分利用多GPU资源，实现真正的并发转录。
