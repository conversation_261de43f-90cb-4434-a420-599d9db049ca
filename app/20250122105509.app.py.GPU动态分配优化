# 导入所需的库
import logging
import os
import uuid
import subprocess
import json
import threading
import gradio as gr
import yt_dlp  # 使用 yt-dlp 替代 youtube_dl
import time
import gc
from gradio.themes.utils import colors
import traceback
import signal
from datetime import datetime
import sys

# 设置工程目录和下载目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 设置transcribe_worker.py的路径
TRANSCRIBE_WORKER_PATH = os.path.join(CURRENT_DIR, "transcribe_worker.py")

# 配置参数
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 14400

# 日志配置
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# GPU与信号量配置
gpu_lock = threading.Lock()
GPUS = ["1", "0"]  # 默认使用GPU2（索引为1），其次使用GPU1（索引为0）
MAX_CONCURRENT_TASKS = 2  # 将并行任务数改为2

class GPUManager:
    def __init__(self):
        self.gpu_states = {gpu: {"in_use": False, "last_used": 0} for gpu in GPUS}
        self.lock = threading.Lock()
    
    def acquire_gpu(self):
        with self.lock:
            # 按优先级顺序查找可用的GPU
            for gpu in GPUS:
                if not self.gpu_states[gpu]["in_use"]:
                    self.gpu_states[gpu]["in_use"] = True
                    self.gpu_states[gpu]["last_used"] = time.time()
                    return gpu
            return None
    
    def release_gpu(self, gpu):
        with self.lock:
            if gpu in self.gpu_states:
                self.gpu_states[gpu]["in_use"] = False

gpu_manager = GPUManager()

# 格式化时间函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    return seconds

# 格式化转录开头信息
def format_transcription_header(title, author, date, duration):
    # 将日期格式从YYYYMMDD转换为YYYY年MM月DD日
    date_str = f"{date[:4]}年{date[4:6]}月{date[6:]}日"
    
    header = f"""视频标题: {title}
作者: {author}
上传日期: {date_str}
视频时长: {duration}秒
==================================================

"""
    return header

# 设置一个环境变量来控制调试模式
DEBUG = os.environ.get('DEBUG', 'false').lower() == 'true'

# 转录音频文件函数
def transcribe_audio(audio_filepath, task, return_timestamps, progress=gr.Progress()):
    global current_task
    task_monitor = TaskMonitor()
    
    try:
        task_monitor.start()
        current_task = {
            'start_time': time.time(),
            'filepath': audio_filepath,
            'monitor': task_monitor
        }
        
        if audio_filepath is None:
            logger.warning("未提交音频文件")
            raise gr.Error("未提交音频文件！请上传音频文件后再提交请求。")

        file_size_mb = os.stat(audio_filepath).st_size / (1024 * 1024)
        if file_size_mb > FILE_LIMIT_MB:
            logger.warning("文件大小超过限制")
            raise gr.Error(
                f"文件大小超过限制。当前文件大小为 {file_size_mb:.2f}MB，限制为 {FILE_LIMIT_MB}MB。"
            )

        # 在任务开始时就获取并锁定GPU
        selected_gpu = gpu_manager.acquire_gpu()
        if selected_gpu is None:
            logger.warning("没有可用的GPU")
            raise gr.Error("所有GPU都在忙，请稍后再试。")

        logger.info(f"已分配 GPU {selected_gpu} 用于任务")

        try:
            env = os.environ.copy()
            env["CUDA_VISIBLE_DEVICES"] = selected_gpu
            
            process = subprocess.Popen(
                [
                    "python", TRANSCRIBE_WORKER_PATH,
                    audio_filepath,
                    task,
                    str(return_timestamps)
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )

            # 设置超时时间为5分钟
            timeout = 300  # 5分钟 = 300秒
            start_time = time.time()
            output_lines = []
            error_lines = []
            process_completed = False

            try:
                while True:
                    # 检查进程是否已经结束
                    if process.poll() is not None:
                        process_completed = True
                        break

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        logger.warning(f"进程 {process.pid} 执行超时（{timeout}秒），正在终止...")
                        process.terminate()
                        try:
                            # 等待2秒让进程正常结束
                            process.wait(2)
                        except subprocess.TimeoutExpired:
                            # 如果2秒后还没结束，强制结束
                            process.kill()
                            logger.warning(f"强制终止进程 {process.pid}")
                        raise gr.Error("转录任务超时。请尝试处理更短的音频文件或检查系统资源使用情况。")

                    # 非阻塞方式读取输出
                    output_line = process.stdout.readline()
                    if output_line:
                        output_lines.append(output_line)
                        if output_line.startswith("progress:"):
                            try:
                                progress_value = float(output_line.strip().split(":")[1])
                                progress(progress_value)
                            except (ValueError, IndexError):
                                pass
                        continue

                    error_line = process.stderr.readline()
                    if error_line:
                        error_lines.append(error_line)
                        continue

                    # 如果没有新的输出，短暂休眠以避免CPU过度使用
                    if not output_line and not error_line:
                        time.sleep(0.1)

                # 读取剩余的输出
                remaining_output, remaining_error = process.communicate()
                if remaining_output:
                    output_lines.append(remaining_output)
                if remaining_error:
                    error_lines.append(remaining_error)

                if process.returncode != 0:
                    error_message = "".join(error_lines)
                    logger.error(f"转录失败: {error_message}")
                    raise gr.Error(f"转录失败: {error_message}")

                # 解析输出结果
                output_text = "".join(output_lines)
                try:
                    # 查找最后一个有效的JSON输出
                    json_outputs = [line for line in output_text.split('\n') if line.strip().startswith('{')]
                    if not json_outputs:
                        raise ValueError("No JSON output found")
                    result = json.loads(json_outputs[-1])
                    return result["text"], result["runtime"]
                except Exception as e:
                    logger.error(f"解析输出失败: {str(e)}")
                    raise gr.Error(f"解析输出失败: {str(e)}")

            finally:
                # 确保在任何情况下都释放GPU
                gpu_manager.release_gpu(selected_gpu)
                logger.info(f"已释放 GPU {selected_gpu}")
                
                # 确保进程被终止
                if not process_completed:
                    try:
                        process.terminate()
                        process.wait(2)
                    except:
                        process.kill()
        except subprocess.TimeoutExpired:
            logger.error("转录任务超时")
            raise gr.Error("转录任务超时，请稍后再试。")
        except Exception as e:
            logger.error(f"转录过程中发生错误: {str(e)}")
            raise gr.Error(f"转录过程中发生错误: {str(e)}")

    finally:
        if task_monitor:
            task_monitor.stop()
        current_task = None

# 只下载音频文件的函数
def download_yt_audio(yt_url, filename, progress_callback=None):
    logger.info(f"开始下载YouTube视频: {yt_url} -> {filename}")
    
    # 定义一组常见的User-Agent，随机使用其中一个
    user_agents = [
        # Windows Chrome
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        # Windows Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        # Mac Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        # Mac Chrome
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ]
    
    import random
    selected_ua = random.choice(user_agents)
    
    ydl_opts = {
        "format": "251/140/250/249",  # 指定音频格式优先级
        "outtmpl": filename,
        "postprocessors": [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'm4a',
            'preferredquality': '192',
        }],
        "postprocessor_args": [
            "-strict", "-2"
        ],
        "prefer_ffmpeg": True,
        "keepvideo": False,
        "progress_hooks": [progress_callback] if progress_callback else [],
        "proxy": "socks5://***********:7893",  # 更新代理端口
        "verbose": True,
        "no_warnings": False,
        # 添加HTTP头信息
        "http_headers": {
            "User-Agent": selected_ua,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.youtube.com/",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache"
        }
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            logger.info(f"使用 User-Agent: {selected_ua}")
            logger.info("开始执行YouTube下载")
            info = ydl.extract_info(yt_url, download=True)
            logger.info("YouTube下载完成")
            
            if info is None:
                raise Exception("无法获取视频信息")
                
            video_info = {
                'title': info.get('title', 'Unknown'),
                'uploader': info.get('uploader', 'Unknown'),
                'upload_date': info.get('upload_date', 'Unknown'),
                'duration': info.get('duration', 0)
            }
            return True, video_info
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False, str(e)

def transcribe_youtube(yt_url, task, return_timestamps, progress=gr.Progress()):
    try:
        if not yt_url or yt_url.strip() == "":
            raise gr.Error("请输入YouTube视频链接")

        # 生成唯一的文件名
        unique_id = str(uuid.uuid4())
        filepath = os.path.join(DOWNLOADS_DIR, f"yt_{unique_id}")
        
        def yt_progress_hook(d):
            if d['status'] == 'downloading':
                total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
                downloaded_bytes = d.get('downloaded_bytes', 0)
                if total_bytes:
                    percentage = downloaded_bytes / total_bytes
                    speed = d.get('speed')
                    if speed is not None and speed > 0:
                        speed_mib_s = speed / (1024 * 1024)
                        speed_str = f"{speed_mib_s:.2f} MiB/s"
                    else:
                        speed_str = "未知速度"
                    progress(percentage, desc=f"下载中... {percentage*100:.2f}% at {speed_str}")
            elif d['status'] == 'finished':
                progress(1, desc="下载完成。准备转录...")
        
        # 下载音频并获取视频信息
        success, video_info = download_yt_audio(yt_url, filepath, progress_callback=yt_progress_hook)
        if not success:
            raise gr.Error(f"下载失败: {video_info}")

        # 添加视频基本信息
        header = format_transcription_header(video_info['title'], video_info['uploader'], video_info['upload_date'], video_info['duration'])

        # 在下载之前就获取并锁定GPU
        selected_gpu = gpu_manager.acquire_gpu()
        if selected_gpu is None:
            logger.warning("没有可用的GPU")
            raise gr.Error("所有GPU都在忙，请稍后再试。")

        logger.info(f"已分配 GPU {selected_gpu} 用于任务")

        try:
            env = os.environ.copy()
            env["CUDA_VISIBLE_DEVICES"] = selected_gpu
            env["WHISPER_TIMESTAMP_INTERVAL"] = "30"  # 设置时间戳间隔为30秒
            
            process = subprocess.Popen(
                [
                    "python", TRANSCRIBE_WORKER_PATH,
                    filepath + ".m4a",
                    task,
                    str(return_timestamps)
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )

            # 设置超时时间为5分钟
            timeout = 300
            start_time = time.time()
            output_lines = []
            error_lines = []
            process_completed = False

            try:
                while True:
                    if process.poll() is not None:
                        process_completed = True
                        break

                    if time.time() - start_time > timeout:
                        logger.warning(f"进程 {process.pid} 执行超时（{timeout}秒），正在终止...")
                        process.terminate()
                        try:
                            # 等待2秒让进程正常结束
                            process.wait(2)
                        except subprocess.TimeoutExpired:
                            # 如果2秒后还没结束，强制结束
                            process.kill()
                            logger.warning(f"强制终止进程 {process.pid}")
                        raise gr.Error("转录任务超时。请尝试处理更短的音频文件或检查系统资源使用情况。")

                    output_line = process.stdout.readline()
                    if output_line:
                        output_lines.append(output_line)
                        if output_line.startswith("progress:"):
                            try:
                                progress_value = float(output_line.strip().split(":")[1])
                                progress(progress_value)
                            except (ValueError, IndexError):
                                pass
                        continue

                    error_line = process.stderr.readline()
                    if error_line:
                        error_lines.append(error_line)
                        continue

                    if not output_line and not error_line:
                        time.sleep(0.1)

                remaining_output, remaining_error = process.communicate()
                if remaining_output:
                    output_lines.append(remaining_output)
                if remaining_error:
                    error_lines.append(remaining_error)

                if process.returncode != 0:
                    error_message = "".join(error_lines)
                    logger.error(f"转录失败: {error_message}")
                    raise gr.Error(f"转录失败: {error_message}")

                output_text = "".join(output_lines)
                try:
                    json_outputs = [line for line in output_text.split('\n') if line.strip().startswith('{')]
                    if not json_outputs:
                        raise ValueError("No JSON output found")
                    result = json.loads(json_outputs[-1])
                    # 在转录结果前添加视频信息
                    result["text"] = header + result["text"]
                    return result["runtime"], result["text"]
                except Exception as e:
                    logger.error(f"解析输出失败: {str(e)}")
                    raise gr.Error(f"解析输出失败: {str(e)}")

            finally:
                # 确保在任何情况下都释放GPU
                gpu_manager.release_gpu(selected_gpu)
                logger.info(f"已释放 GPU {selected_gpu}")
                
                # 确保进程被终止
                if not process_completed:
                    try:
                        process.terminate()
                        process.wait(2)
                    except:
                        process.kill()

        finally:
            # 清理临时文件
            try:
                os.remove(filepath + ".m4a")
            except:
                pass

    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        raise gr.Error(f"处理失败: {str(e)}")

# 添加任务超时监控机制
TASK_TIMEOUT = 300  # 5分钟超时
task_start_time = None
current_task = None

class TaskMonitor:
    def __init__(self, timeout=TASK_TIMEOUT):
        self.timeout = timeout
        self.start_time = None
        self.monitoring = False
        self.monitor_thread = None

    def start(self):
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_task, daemon=True)
        self.monitor_thread.start()

    def stop(self):
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def _monitor_task(self):
        while self.monitoring:
            if time.time() - self.start_time > self.timeout:
                logger.warning("主进程任务超时，正在终止程序...")
                # 发送SIGTERM信号给自己（主进程）
                os.kill(os.getpid(), signal.SIGTERM)
                break
            time.sleep(1)

# 添加信号处理函数
def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，正在清理资源...")
    # 释放所有GPU信号量
    for gpu in GPUS:
        try:
            gpu_manager.release_gpu(gpu)
        except:
            pass
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

def check_ytdlp_update():
    try:
        logger.info("开始更新 yt-dlp...")
        # 使用subprocess运行yt-dlp更新命令
        process = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "yt-dlp"],
                               capture_output=True, text=True)
        
        update_message = ""
        if process.returncode == 0:
            update_message = "yt-dlp 更新成功！\n" + process.stdout
        else:
            update_message = "yt-dlp 更新失败：\n" + process.stderr
            return update_message
        
        # 获取启动脚本路径
        start_script = "/home/<USER>/start_whisper_jax.sh"
        if not os.path.exists(start_script):
            return update_message + "\n错误：找不到启动脚本，无法重启服务"
            
        logger.info("正在重启服务...")
        # 执行重启命令
        restart_process = subprocess.run([start_script, "restart"],
                                       capture_output=True, text=True)
        
        if restart_process.returncode == 0:
            return update_message + "\n服务重启成功！\n" + restart_process.stdout
        else:
            return update_message + "\n服务重启失败：\n" + restart_process.stderr
            
    except Exception as e:
        error_msg = f"更新过程中发生错误：{str(e)}\n"
        error_msg += traceback.format_exc()
        logger.error(error_msg)
        return error_msg

# 创建Gradio界面
def create_gradio_interface():
    with gr.Blocks(theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎙️ Whisper-JAX 转录服务")
        
        with gr.Tabs():
            # YouTube 标签页
            with gr.Tab("YouTube"):
                groups = []
                for i in range(10):
                    with gr.Group():
                        gr.Markdown(f"### 转录任务 {i+1}")
                        yt_url = gr.Text(label="YouTube视频链接")
                        task = gr.Radio(
                            choices=["transcribe", "translate"],
                            value="transcribe",
                            label="任务类型"
                        )
                        return_timestamps = gr.Checkbox(
                            value=True,
                            label="显示时间戳"
                        )
                        transcription_time = gr.Text(
                            label="转录用时",
                            interactive=False
                        )
                        transcription = gr.Text(
                            label="转录结果",
                            interactive=False,
                            lines=10
                        )
                        transcribe_button = gr.Button("开始转录")
                        transcribe_button.click(
                            fn=transcribe_youtube,
                            inputs=[yt_url, task, return_timestamps],
                            outputs=[transcription_time, transcription],
                            show_progress=True,
                            concurrency_limit=MAX_CONCURRENT_TASKS
                        )
                        if i < 9:
                            gr.Markdown("---")
                        groups.append((yt_url, transcription))

            # 麦克风 标签页
            with gr.Tab("麦克风"):
                with gr.Column():
                    audio_file = gr.Audio(type="filepath", label="音频文件")
                    task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                    return_timestamps_mic = gr.Checkbox(value=True, label="返回时间戳")
                    transcribe_btn_mic = gr.Button("转录")
                    transcription_time_mic = gr.Text(label="转录时间 (秒)", interactive=False)
                    transcription_mic = gr.Text(label="转录内容", lines=30, show_copy_button=True)
                    transcribe_btn_mic.click(
                        fn=transcribe_audio,
                        inputs=[audio_file, task_mic, return_timestamps_mic],
                        outputs=[transcription_time_mic, transcription_mic],
                        show_progress=True,
                        concurrency_limit=MAX_CONCURRENT_TASKS
                    )

            # 音频文件 标签页
            with gr.Tab("音频文件"):
                with gr.Column():
                    upload_audio = gr.Audio(label="音频文件", type="filepath")
                    task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                    return_timestamps_upload = gr.Checkbox(value=True, label="返回时间戳")
                    transcribe_btn_upload = gr.Button("转录")
                    transcription_time_upload = gr.Text(label="转录时间 (秒)", interactive=False)
                    transcription_upload = gr.Text(label="转录内容", lines=30, show_copy_button=True)
                    transcribe_btn_upload.click(
                        fn=transcribe_audio,
                        inputs=[upload_audio, task_upload, return_timestamps_upload],
                        outputs=[transcription_time_upload, transcription_upload],
                        show_progress=True,
                        concurrency_limit=MAX_CONCURRENT_TASKS
                    )

            # 设置 标签页
            with gr.Tab("设置"):
                with gr.Group():
                    gr.Markdown("### YouTube下载器设置")
                    update_button = gr.Button("检查并更新yt-dlp")
                    update_status = gr.Textbox(label="更新状态", interactive=False)
                    update_button.click(
                        fn=check_ytdlp_update,
                        inputs=[],
                        outputs=[update_status]
                    )

        gr.Markdown("Whisper large-v3模型由OpenAI提供。后端通过TRC计划支持的TPU v4-8运行。")
    return demo

def main():
    # 创建Gradio界面
    interface = create_gradio_interface()
    
    # 添加优雅关闭处理
    def graceful_shutdown(*args):
        logger.info("正在关闭服务...")
        # 释放所有GPU信号量
        for gpu in GPUS:
            try:
                gpu_manager.release_gpu(gpu)
            except:
                pass
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGTERM, graceful_shutdown)
    signal.signal(signal.SIGINT, graceful_shutdown)
    
    # 启动服务
    interface.launch(
        server_name="0.0.0.0",
        server_port=7850,
        share=False,
        show_error=True,
        max_threads=MAX_CONCURRENT_TASKS
    )

if __name__ == "__main__":
    main()
