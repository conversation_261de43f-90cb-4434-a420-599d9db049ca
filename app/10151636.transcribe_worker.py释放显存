# transcribe_worker.py
import sys
import json
import os
import time
import jax.numpy as jnp
import numpy as np
from transformers.pipelines.audio_utils import ffmpeg_read
from whisper_jax import FlaxWhisperPipline
import logging

def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    else:
        return seconds  # 处理格式错误的时间戳

def main():
    # 设置子进程日志
    logger = logging.getLogger("transcribe_worker")
    logger.setLevel(logging.INFO)
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    
    if len(sys.argv) != 4:
        logger.error("Invalid number of arguments")
        print(json.dumps({"error": "Invalid arguments"}))
        sys.exit(1)
    
    audio_filepath = sys.argv[1]
    task = sys.argv[2]
    return_timestamps = sys.argv[3].lower() == 'true'
    
    # 设置使用的GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 指定使用GPU 0
    
    # 初始化JAX编译缓存路径
    from jax.experimental.compilation_cache import compilation_cache as cc
    cc.set_cache_dir("./jax_cache")
    
    # 设置使用的Whisper模型检查点
    checkpoint = "openai/whisper-large-v3"
    
    # 配置不同参数
    BATCH_SIZE = 32
    CHUNK_LENGTH_S = 30
    
    # 初始化模型管道
    logger.info("初始化模型管道...")
    pipeline = FlaxWhisperPipline(checkpoint, dtype=jnp.bfloat16, batch_size=BATCH_SIZE)
    stride_length_s = CHUNK_LENGTH_S / 6
    chunk_len = round(CHUNK_LENGTH_S * pipeline.feature_extractor.sampling_rate)
    stride_left = stride_right = round(stride_length_s * pipeline.feature_extractor.sampling_rate)
    step = chunk_len - stride_left - stride_right
    
    # 预编译模型，以提高首次运行性能
    logger.info("预编译模型...")
    random_inputs = {
        "input_features": np.ones(
            (BATCH_SIZE, pipeline.model.config.num_mel_bins, 2 * pipeline.model.config.max_source_positions)
        )
    }
    _ = pipeline.forward(random_inputs, batch_size=BATCH_SIZE, return_timestamps=True)
    
    # 读取并转换音频文件
    logger.info(f"读取音频文件: {audio_filepath}")
    try:
        with open(audio_filepath, "rb") as f:
            inputs_data = f.read()
    except Exception as e:
        logger.error(f"无法读取音频文件: {e}")
        print(json.dumps({"error": f"无法读取音频文件: {e}"}))
        sys.exit(1)
    
    inputs = ffmpeg_read(inputs_data, pipeline.feature_extractor.sampling_rate)
    inputs_prepared = {"array": inputs, "sampling_rate": pipeline.feature_extractor.sampling_rate}
    
    # 预处理音频输入
    logger.info("预处理音频输入...")
    dataloader = pipeline.preprocess_batch(inputs_prepared, chunk_length_s=CHUNK_LENGTH_S, batch_size=BATCH_SIZE)
    dataloader = list(map(lambda x: x, dataloader))  # 简单映射
    
    model_outputs = []
    start_time = time.time()
    
    # 用模型进行转录
    logger.info("开始转录...")
    for batch in dataloader:
        model_outputs.append(pipeline.forward(batch, batch_size=BATCH_SIZE, task=task, return_timestamps=True))
    runtime = time.time() - start_time
    logger.info(f"转录完成，耗时 {runtime:.2f} 秒")
    
    # 后处理结果
    logger.info("后处理结果...")
    post_processed = pipeline.postprocess(model_outputs, return_timestamps=True)
    text = post_processed["text"]
    if return_timestamps:
        timestamps = post_processed.get("chunks")
        timestamps = [
            f"[{format_timestamp(chunk['timestamp'][0])} -> {format_timestamp(chunk['timestamp'][1])}] {chunk['text']}"
            for chunk in timestamps
        ]
        text = "\n".join(str(feature) for feature in timestamps)
    logger.info("后处理完成。")
    
    # 输出结果为JSON
    result = {
        "text": text,
        "runtime": runtime
    }
    print(json.dumps(result))
    logger.info("转录任务结束，子进程退出。")
    sys.exit(0)

if __name__ == "__main__":
    main()
