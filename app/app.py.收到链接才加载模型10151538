import multiprocessing as mp
import logging
import math
import os
import time
import uuid
import threading  # 导入 threading 以实现 ModelManager
from multiprocessing import Pool

import gradio as gr  # 用于构建用户界面的库
import jax.numpy as jnp  # JAX的numpy版本，用于高效的数值计算
import numpy as np  # 经典的numpy库
import yt_dlp as youtube_dl  # YouTube下载器，用于提取音频
from jax.experimental.compilation_cache import compilation_cache as cc  # JAX编译缓存
from transformers.models.whisper.tokenization_whisper import TO_LANGUAGE_CODE  # Whisper模型语言代码
from transformers.pipelines.audio_utils import ffmpeg_read  # 用于音频读取的工具

from whisper_jax import FlaxWhisperPipline  # Whisper JAX模型管道

# 设置 multiprocessing 启动方法为 'spawn'
try:
    mp.set_start_method('spawn')
except RuntimeError:
    pass  # 如果已经设置过，忽略错误

# 设置使用的GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 指定使用GPU 0

# 初始化JAX编译缓存路径
cc.set_cache_dir("./jax_cache")

# 设置下载目录为工程目录下的 'downloads' 文件夹
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(CURRENT_DIR, "downloads")
os.makedirs(DOWNLOADS_DIR, exist_ok=True)

# 设置使用的Whisper模型检查点
checkpoint = "openai/whisper-large-v3"

# 配置不同参数
BATCH_SIZE = 32
CHUNK_LENGTH_S = 30
NUM_PROC = 32
FILE_LIMIT_MB = 1000
YT_LENGTH_LIMIT_S = 7200  # YouTube文件长度限制为2小时

# 应用程序标题和描述
title = "Whisper JAX: The Fastest Whisper API ⚡️"
description = """Whisper JAX是OpenAI Whisper模型的优化实现。它在JAX上运行，后端使用TPU v4-8。相较于PyTorch在A100 GPU上的运行速度，它快了超过70倍。"""
article = "Whisper large-v3模型由OpenAI提供。后端通过TRC计划支持的TPU v4-8运行。"

# 获取支持的语言名称并排序
language_names = sorted(TO_LANGUAGE_CODE.keys())

# 设置日志记录
logger = logging.getLogger("whisper-jax-app")
logger.setLevel(logging.INFO)
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s;%(levelname)s;%(message)s", "%Y-%m-%d %H:%M:%S")
ch.setFormatter(formatter)
logger.addHandler(ch)

# 全局变量，用于存储 ModelManager 实例
model_manager = None

# 简单的身份函数，用于多进程映射
def identity(batch):
    return batch

# 格式化时间戳函数
def format_timestamp(seconds: float, always_include_hours: bool = False, decimal_marker: str = "."):
    if seconds is not None:
        milliseconds = round(seconds * 1000.0)
        hours = milliseconds // 3_600_000
        milliseconds -= hours * 3_600_000
        minutes = milliseconds // 60_000
        milliseconds -= minutes * 60_000
        seconds = milliseconds // 1_000
        milliseconds -= seconds * 1_000
        hours_marker = f"{hours:02d}:" if always_include_hours or hours > 0 else ""
        return f"{hours_marker}{minutes:02d}:{seconds:02d}{decimal_marker}{milliseconds:03d}"
    else:
        return seconds  # 处理格式错误的时间戳

# 定义 ModelManager 类
class ModelManager:
    def __init__(self, checkpoint, dtype, batch_size, chunk_length_s, pool, logger):
        self.checkpoint = checkpoint
        self.dtype = dtype
        self.batch_size = batch_size
        self.chunk_length_s = chunk_length_s
        self.pool = pool
        self.logger = logger
        self.pipeline = None
        self.lock = threading.Lock()
        self.timer = None
        self.timeout = 10 * 60  # 10 分钟

    def load_model(self):
        with self.lock:
            if self.pipeline is None:
                self.logger.info("加载模型...")
                self.pipeline = FlaxWhisperPipline(self.checkpoint, dtype=self.dtype, batch_size=self.batch_size)
                stride_length_s = self.chunk_length_s / 6
                self.chunk_len = round(self.chunk_length_s * self.pipeline.feature_extractor.sampling_rate)
                self.stride_left = self.stride_right = round(stride_length_s * self.pipeline.feature_extractor.sampling_rate)
                self.step = self.chunk_len - self.stride_left - self.stride_right
                # 预编译模型，以提高首次运行性能
                self.logger.info("编译前向调用...")
                start = time.time()
                random_inputs = {
                    "input_features": np.ones(
                        (self.batch_size, self.pipeline.model.config.num_mel_bins, 2 * self.pipeline.model.config.max_source_positions)
                    )
                }
                _ = self.pipeline.forward(random_inputs, batch_size=self.batch_size, return_timestamps=True)
                compile_time = time.time() - start
                self.logger.info(f"编译完成，耗时 {compile_time:.2f} 秒")
            self.reset_timer()

    def reset_timer(self):
        if self.timer:
            self.timer.cancel()
        self.timer = threading.Timer(self.timeout, self.unload_model)
        self.timer.start()

    def unload_model(self):
        with self.lock:
            if self.pipeline:
                self.logger.info("卸载模型...")
                del self.pipeline
                self.pipeline = None
                self.logger.info("模型已卸载。")

    def get_pipeline(self):
        with self.lock:
            return self.pipeline

# 音频生成函数，处理和转录音频块
def tqdm_generate(inputs: dict, task: str, return_timestamps: bool, progress: gr.Progress):
    global model_manager
    pipeline = model_manager.get_pipeline()
    if pipeline is None:
        raise RuntimeError("模型未加载。")

    inputs_len = inputs["array"].shape[0]
    all_chunk_start_idx = np.arange(0, inputs_len, model_manager.step)
    num_samples = len(all_chunk_start_idx)
    num_batches = math.ceil(num_samples / BATCH_SIZE)
    dummy_batches = list(range(num_batches))

    # 预处理音频输入
    dataloader = pipeline.preprocess_batch(inputs, chunk_length_s=CHUNK_LENGTH_S, batch_size=BATCH_SIZE)
    progress(0, desc="预处理音频文件...")
    logger.info("预处理音频文件...")
    dataloader = model_manager.pool.map(identity, dataloader)
    logger.info("预处理完成。")

    model_outputs = []
    start_time = time.time()
    logger.info("开始转录...")

    # 用模型进行转录
    for batch, _ in zip(dataloader, progress.tqdm(dummy_batches, desc="转录中...")):
        model_outputs.append(pipeline.forward(batch, batch_size=BATCH_SIZE, task=task, return_timestamps=True))
    runtime = time.time() - start_time
    logger.info("转录完成。")

    # 后处理结果
    logger.info("后处理结果...")
    post_processed = pipeline.postprocess(model_outputs, return_timestamps=True)
    text = post_processed["text"]
    if return_timestamps:
        timestamps = post_processed.get("chunks")
        timestamps = [
            f"[{format_timestamp(chunk['timestamp'][0])} -> {format_timestamp(chunk['timestamp'][1])}] {chunk['text']}"
            for chunk in timestamps
        ]
        text = "\n".join(str(feature) for feature in timestamps)
    logger.info("后处理完成。")

    # 重置计时器，因为有新的请求被处理
    model_manager.reset_timer()

    return text, runtime

# 处理传入音频块的转录函数
def transcribe_chunked_audio(inputs, task, return_timestamps, progress=gr.Progress()):
    global model_manager
    if inputs is None:
        logger.warning("未提交音频文件")
        raise gr.Error("未提交音频文件！请上传音频文件后再提交请求。")
    file_size_mb = os.stat(inputs).st_size / (1024 * 1024)
    if file_size_mb > FILE_LIMIT_MB:
        logger.warning("文件大小超过限制")
        raise gr.Error(
            f"文件大小超过限制。当前文件大小为 {file_size_mb:.2f}MB，限制为 {FILE_LIMIT_MB}MB。"
        )

    # 确保在处理前加载模型
    model_manager.load_model()
    pipeline = model_manager.get_pipeline()
    if pipeline is None:
        raise RuntimeError("模型未加载。")

    progress(0, desc="加载音频文件...")
    logger.info("加载音频文件...")

    # 读取并转换音频文件
    with open(inputs, "rb") as f:
        inputs_data = f.read()

    sampling_rate = pipeline.feature_extractor.sampling_rate
    inputs_data = ffmpeg_read(inputs_data, sampling_rate)
    inputs_prepared = {"array": inputs_data, "sampling_rate": sampling_rate}
    logger.info("音频文件加载完成。")

    text, runtime = tqdm_generate(inputs_prepared, task=task, return_timestamps=return_timestamps, progress=progress)
    return text, runtime

# 下载YouTube音频
def download_yt_audio(yt_url, filename, progress_callback=None):
    ydl_opts = {
        "outtmpl": filename,
        "format": "worstvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
        "progress_hooks": [progress_callback] if progress_callback else [],
    }
    with youtube_dl.YoutubeDL(ydl_opts) as ydl:
        try:
            ydl.download([yt_url])
        except youtube_dl.utils.ExtractorError as err:
            raise gr.Error(str(err))

# 转录来自YouTube的视频
def transcribe_youtube(yt_url, task, return_timestamps, progress=gr.Progress()):
    global model_manager
    logger.info("开始处理YouTube视频转录请求...")
    progress(0, desc="加载YouTube视频...")
    logger.info("加载YouTube视频...")

    # 定义进度回调函数
    def yt_progress_hook(d):
        if d['status'] == 'downloading':
            total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
            downloaded_bytes = d.get('downloaded_bytes', 0)
            if total_bytes:
                percentage = downloaded_bytes / total_bytes
                speed = d.get('speed')
                if speed is not None and speed > 0:
                    speed_mib_s = speed / (1024 * 1024)
                    speed_str = f"{speed_mib_s:.2f} MiB/s"
                else:
                    speed_str = "未知速度"
                progress(percentage, desc=f"下载中... {percentage*100:.2f}% at {speed_str}")
        elif d['status'] == 'finished':
            progress(1, desc="下载完成。")

    # 生成唯一文件名
    unique_id = uuid.uuid4().hex
    filepath = os.path.join(DOWNLOADS_DIR, f"video_{unique_id}.mp4")

    download_yt_audio(yt_url, filepath, progress_callback=yt_progress_hook)

    # 确保在处理前加载模型
    model_manager.load_model()
    pipeline = model_manager.get_pipeline()
    if pipeline is None:
        raise RuntimeError("模型未加载。")

    with open(filepath, "rb") as f:
        inputs_data = f.read()

    sampling_rate = pipeline.feature_extractor.sampling_rate
    inputs_data = ffmpeg_read(inputs_data, sampling_rate)
    inputs_prepared = {"array": inputs_data, "sampling_rate": sampling_rate}

    logger.info("音频文件加载完成。")
    runtime, text = tqdm_generate(inputs_prepared, task=task, return_timestamps=return_timestamps, progress=progress)

    # 可选：处理完毕后删除下载的文件以节省磁盘空间
    try:
        os.remove(filepath)
        logger.info(f"已删除临时文件: {filepath}")
    except OSError as e:
        logger.warning(f"无法删除临时文件 {filepath}: {e}")

    return runtime, text  # html_embed_str

# 定义整个应用的界面
def create_gradio_interface():
    with gr.Blocks() as demo:
        gr.Markdown(f"## {title}")
        gr.Markdown(description)

        # YouTube 标签页
        with gr.Tab("YouTube"):
            with gr.Column():
                yt_url = gr.Textbox(lines=1, placeholder="在此粘贴YouTube视频的URL", label="YouTube URL")
                task = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn = gr.Button("转录")
                transcription_time = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn.click(
                    fn=transcribe_youtube,
                    inputs=[yt_url, task, return_timestamps],
                    outputs=[transcription_time, transcription]
                )

        # Microphone 标签页
        with gr.Tab("麦克风"):
            with gr.Column():
                audio_file = gr.Audio(type="filepath", label="音频文件")
                task_mic = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_mic = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn_mic = gr.Button("转录")
                transcription_time_mic = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_mic = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn_mic.click(
                    fn=transcribe_chunked_audio,
                    inputs=[audio_file, task_mic, return_timestamps_mic],
                    outputs=[transcription_time_mic, transcription_mic]
                )

        # Audio File 标签页
        with gr.Tab("音频文件"):
            with gr.Column():
                upload_audio = gr.Audio(label="音频文件", type="filepath")
                task_upload = gr.Radio(["transcribe", "translate"], label="任务", value="transcribe")
                return_timestamps_upload = gr.Checkbox(value=False, label="返回时间戳")
                transcribe_btn_upload = gr.Button("转录")
                transcription_time_upload = gr.Textbox(label="转录时间 (秒)", interactive=False)
                transcription_upload = gr.Textbox(label="转录内容", show_copy_button=True, lines=30)
                transcribe_btn_upload.click(
                    fn=transcribe_chunked_audio,
                    inputs=[upload_audio, task_upload, return_timestamps_upload],
                    outputs=[transcription_time_upload, transcription_upload]
                )

        gr.Markdown(article)
    return demo

def main():
    global model_manager
    # 初始化进程池
    pool = Pool(NUM_PROC)  # 创建进程池用于并行计算

    # 初始化 ModelManager
    model_manager = ModelManager(
        checkpoint=checkpoint,
        dtype=jnp.bfloat16,
        batch_size=BATCH_SIZE,
        chunk_length_s=CHUNK_LENGTH_S,
        pool=pool,
        logger=logger
    )

    # 创建 Gradio 界面
    demo = create_gradio_interface()

    # 启动应用
    demo.queue(max_size=5)
    demo.launch(server_name="0.0.0.0", show_api=False)

if __name__ == "__main__":
    main()