#!/usr/bin/env python3
"""
Gradio并发测试脚本
测试Gradio界面的并发处理能力
"""

import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 服务器配置
SERVER_URL = "http://localhost:7850"

def test_gradio_queue():
    """
    测试Gradio队列状态
    """
    try:
        # 获取队列状态
        response = requests.get(f"{SERVER_URL}/queue/status")
        if response.status_code == 200:
            queue_status = response.json()
            print(f"队列状态: {queue_status}")
            return queue_status
        else:
            print(f"无法获取队列状态，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"获取队列状态时出错: {str(e)}")
        return None

def submit_transcription_task(task_id, test_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ"):
    """
    提交转录任务到Gradio
    """
    print(f"[任务 {task_id}] 开始提交转录任务...")
    
    try:
        # 首先获取session hash
        response = requests.post(f"{SERVER_URL}/queue/join", json={
            "data": [
                test_url,      # YouTube URL
                "transcribe",  # 任务类型
                False,         # 返回时间戳
                ["medium - Whisper大型模型，准确性高，速度较慢", "medium"]  # 模型选择
            ],
            "event_data": None,
            "fn_index": 0,  # YouTube转录功能的索引
            "trigger_id": 10,
            "session_hash": f"test_session_{task_id}_{int(time.time())}"
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"[任务 {task_id}] 任务已提交到队列")
            print(f"[任务 {task_id}] 响应: {result}")
            return result
        else:
            print(f"[任务 {task_id}] 提交失败，状态码: {response.status_code}")
            print(f"[任务 {task_id}] 响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"[任务 {task_id}] 提交时发生错误: {str(e)}")
        return None

def monitor_queue_status(duration=60):
    """
    监控队列状态
    """
    print(f"开始监控队列状态，持续 {duration} 秒...")
    start_time = time.time()
    
    while time.time() - start_time < duration:
        queue_status = test_gradio_queue()
        if queue_status:
            # 检查队列中的任务数量
            if 'queue' in queue_status:
                queue_size = len(queue_status['queue'])
                print(f"当前队列大小: {queue_size}")
            
            if 'queue_eta' in queue_status:
                print(f"预计等待时间: {queue_status['queue_eta']}")
        
        time.sleep(5)  # 每5秒检查一次

def test_concurrent_submission():
    """
    测试并发提交
    """
    print("开始并发提交测试...")
    print("=" * 50)
    
    # 首先检查初始队列状态
    print("初始队列状态:")
    test_gradio_queue()
    
    # 使用线程池同时提交多个任务
    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交3个任务
        futures = []
        for i in range(3):
            future = executor.submit(submit_transcription_task, i + 1)
            futures.append(future)
            time.sleep(1)  # 间隔1秒提交，模拟用户操作
        
        # 等待所有提交完成
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    print("\n提交完成，开始监控队列状态...")
    
    # 监控队列状态
    monitor_queue_status(30)  # 监控30秒
    
    return results

def test_server_connection():
    """
    测试服务器连接
    """
    try:
        response = requests.get(SERVER_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False

def main():
    print("Gradio并发测试工具")
    print("=" * 50)
    
    # 检查服务器连接
    if not test_server_connection():
        print("请确保Whisper服务正在运行在 http://localhost:7850")
        return
    
    # 测试初始队列状态
    print("\n检查初始队列状态:")
    test_gradio_queue()
    
    # 执行并发提交测试
    print("\n开始并发提交测试...")
    results = test_concurrent_submission()
    
    print("\n测试完成！")
    print("=" * 50)
    
    # 分析结果
    successful_submissions = [r for r in results if r is not None]
    print(f"成功提交的任务数: {len(successful_submissions)}")
    print(f"失败的任务数: {len(results) - len(successful_submissions)}")
    
    if len(successful_submissions) >= 2:
        print("✅ 多个任务成功提交到队列，并发功能可能正常工作")
    else:
        print("❌ 只有少数任务成功提交，可能存在并发问题")
    
    # 最终队列状态
    print("\n最终队列状态:")
    test_gradio_queue()

if __name__ == "__main__":
    main()
