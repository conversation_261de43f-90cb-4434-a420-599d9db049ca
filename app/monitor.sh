#!/bin/bash

# 设置应用程序路径
APP_DIR="$HOME/whisper-jax/app"
APP_SCRIPT="app.py"
LOG_FILE="$APP_DIR/app.log"
ERROR_LOG_FILE="$APP_DIR/error.log"
PID_FILE="$APP_DIR/app.pid"

# 确保目录存在
cd "$APP_DIR" || exit 1

# 检查并激活虚拟环境
if [ -f "$HOME/whisper-jax/venv/bin/activate" ]; then
    source "$HOME/whisper-jax/venv/bin/activate"
elif [ -f "$HOME/.local/share/virtualenvs/whisper-jax/bin/activate" ]; then
    source "$HOME/.local/share/virtualenvs/whisper-jax/bin/activate"
elif [ -f "$HOME/anaconda3/envs/whisper-jax/bin/activate" ]; then
    source "$HOME/anaconda3/envs/whisper-jax/bin/activate"
else
    echo "$(date): 错误: 找不到虚拟环境" >> "$LOG_FILE"
    exit 1
fi

# 检查程序是否在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 程序正在运行
        fi
    fi
    return 1  # 程序没有运行
}

# 启动程序
start_app() {
    echo "$(date): 启动应用程序..." >> "$LOG_FILE"
    # 使用完整的 Python 路径启动程序
    PYTHON_PATH=$(which python)
    echo "$(date): 使用 Python: $PYTHON_PATH" >> "$LOG_FILE"
    
    # 检查必要的依赖是否安装
    echo "$(date): 检查 Python 依赖..." >> "$LOG_FILE"
    $PYTHON_PATH -c "import gradio, jax, transformers" 2>> "$ERROR_LOG_FILE"
    if [ $? -ne 0 ]; then
        echo "$(date): 错误: 缺少必要的 Python 依赖" >> "$LOG_FILE"
        cat "$ERROR_LOG_FILE" >> "$LOG_FILE"
        return 1
    fi
    
    # 使用 nohup 在后台运行程序，同时捕获标准输出和错误输出
    nohup $PYTHON_PATH "$APP_SCRIPT" > >(tee -a "$LOG_FILE") 2> >(tee -a "$ERROR_LOG_FILE" >&2) &
    
    # 保存 PID
    echo $! > "$PID_FILE"
    echo "$(date): 应用程序已启动，PID: $(cat $PID_FILE)" >> "$LOG_FILE"
    
    # 等待程序启动，并检查是否成功
    sleep 30  # 增加初始等待时间
    if ! check_running; then
        echo "$(date): 错误: 应用程序启动失败" >> "$LOG_FILE"
        echo "$(date): 错误日志:" >> "$LOG_FILE"
        cat "$ERROR_LOG_FILE" >> "$LOG_FILE"
        return 1
    fi
    
    # 检查端口是否开放，增加等待时间到5分钟
    for i in {1..60}; do  # 等待最多5分钟
        if nc -z localhost 7860; then
            echo "$(date): 应用程序端口 7860 已开放" >> "$LOG_FILE"
            # 额外等待30秒确保应用完全初始化
            sleep 30
            return 0
        fi
        echo "$(date): 等待端口开放，尝试 $i/60..." >> "$LOG_FILE"
        sleep 5
    done
    
    echo "$(date): 错误: 应用程序端口未能在5分钟内开放" >> "$LOG_FILE"
    # 检查是否有错误日志
    if [ -s "$ERROR_LOG_FILE" ]; then
        echo "$(date): 错误日志内容:" >> "$LOG_FILE"
        cat "$ERROR_LOG_FILE" >> "$LOG_FILE"
    fi
    return 1
}

# 停止程序
stop_app() {
    if [ -f "$PID_FILE" ]; then
        pid=$(cat "$PID_FILE")
        echo "$(date): 停止应用程序 (PID: $pid)..." >> "$LOG_FILE"
        kill "$pid" 2>/dev/null
        rm -f "$PID_FILE"
        sleep 2
    fi
}

# 重启程序
restart_app() {
    stop_app
    start_app
}

# 主循环
while true; do
    if ! check_running; then
        echo "$(date): 检测到应用程序未运行" >> "$LOG_FILE"
        restart_app
    else
        # 检查程序是否响应（可以通过检查端口是否开放来验证）
        if ! nc -z localhost 7860; then
            echo "$(date): 检测到应用程序端口未响应，等待30秒后重试..." >> "$LOG_FILE"
            sleep 30
            if ! nc -z localhost 7860; then
                echo "$(date): 端口仍然未响应，重启应用程序" >> "$LOG_FILE"
                restart_app
            fi
        fi
    fi
    
    # 每120秒检查一次
    sleep 120
done
