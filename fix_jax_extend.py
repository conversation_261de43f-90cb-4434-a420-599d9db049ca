import sys
import jax
import types

# 创建一个虚拟的jax.extend模块
class DummyLinearUtil:
    def wrap_init(self, f):
        return f

    def cache(self, f):
        return f

# 创建extend模块和linear_util子模块
extend_module = types.ModuleType('jax.extend')
linear_util_module = types.ModuleType('jax.extend.linear_util')

# 设置linear_util模块的属性
linear_util_module.wrap_init = DummyLinearUtil().wrap_init
linear_util_module.cache = DummyLinearUtil().cache

# 将linear_util模块添加到extend模块
extend_module.linear_util = linear_util_module

# 将extend模块添加到jax包
sys.modules['jax.extend'] = extend_module
sys.modules['jax.extend.linear_util'] = linear_util_module

# 打印信息
print("已添加虚拟的jax.extend.linear_util模块")
