#!/bin/bash

# 设置路径与日志文件
START_SCRIPT="/home/<USER>/start_whisper_jax.sh"
APP_DIR="/home/<USER>/whisper-jax"
LOG_FILE="$APP_DIR/whisper_monitor.log"

check_service() {
    status=$("$START_SCRIPT" status)
    if echo "$status" | grep -q "服务未运行"; then
        echo "$(date): 检测到服务未运行，正在重启..." >> "$LOG_FILE"
        "$START_SCRIPT" restart >> "$LOG_FILE" 2>&1
        sleep 5
        status=$("$START_SCRIPT" status)
        if echo "$status" | grep -q "服务正在运行"; then
            echo "$(date): 服务已成功重启" >> "$LOG_FILE"
        else
            echo "$(date): 服务重启失败" >> "$LOG_FILE"
        fi
    fi
}

# 初次检测服务状态
check_service

# 每30秒检测一次服务状态
while true; do
    sleep 30
    check_service
done