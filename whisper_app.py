import os
import sys
import tempfile
import argparse
import whisper
import time
import logging
import yt_dlp
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s;%(levelname)s;%(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def download_youtube_audio(url, output_path):
    """从YouTube下载音频"""
    logger.info(f"正在从YouTube下载: {url}")
    
    ydl_opts = {
        'format': 'bestaudio/best',
        'outtmpl': output_path,
        'quiet': True,
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])
    
    logger.info(f"下载完成: {output_path}")
    return output_path

def transcribe_audio(audio_path, model_name="base", language=None, task="transcribe", return_timestamps=False):
    """使用Whisper转录音频"""
    logger.info(f"正在加载模型: {model_name}")
    model = whisper.load_model(model_name)
    
    logger.info(f"开始转录: {audio_path}")
    start_time = time.time()
    
    # 转录选项
    options = {
        "task": task,
        "return_timestamps": return_timestamps
    }
    
    if language:
        options["language"] = language
    
    # 执行转录
    result = model.transcribe(audio_path, **options)
    
    end_time = time.time()
    logger.info(f"转录完成，耗时: {end_time - start_time:.2f}秒")
    
    return result

def save_transcript(result, output_file=None):
    """保存转录结果"""
    if output_file:
        logger.info(f"正在保存转录结果到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result["text"])
        
        # 如果有时间戳，也保存时间戳
        if "segments" in result:
            timestamp_file = os.path.splitext(output_file)[0] + "_timestamps.txt"
            with open(timestamp_file, 'w', encoding='utf-8') as f:
                for segment in result["segments"]:
                    start = segment["start"]
                    end = segment["end"]
                    text = segment["text"]
                    f.write(f"[{start:.2f} - {end:.2f}] {text}\n")
            logger.info(f"时间戳已保存到: {timestamp_file}")
    
    return result["text"]

def cleanup_temp_files(temp_files):
    """清理临时文件"""
    for file_path in temp_files:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                else:
                    os.remove(file_path)
                logger.info(f"已删除临时文件: {file_path}")
            except Exception as e:
                logger.error(f"删除临时文件失败: {file_path}, 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description="Whisper音频转录工具")
    parser.add_argument("--audio", type=str, help="音频文件路径")
    parser.add_argument("--youtube", type=str, help="YouTube视频URL")
    parser.add_argument("--model", type=str, default="base", help="Whisper模型名称 (tiny, base, small, medium, large)")
    parser.add_argument("--language", type=str, help="音频语言代码 (如zh, en)")
    parser.add_argument("--task", type=str, default="transcribe", choices=["transcribe", "translate"], help="任务类型: transcribe或translate")
    parser.add_argument("--timestamps", action="store_true", help="是否包含时间戳")
    parser.add_argument("--output", type=str, help="输出文件路径")
    
    args = parser.parse_args()
    
    # 临时文件列表，用于后续清理
    temp_files = []
    
    try:
        # 处理输入源
        audio_path = None
        if args.audio:
            audio_path = args.audio
        elif args.youtube:
            # 创建临时目录用于下载
            temp_dir = tempfile.mkdtemp()
            temp_files.append(temp_dir)
            
            temp_audio = os.path.join(temp_dir, "audio.mp4")
            audio_path = download_youtube_audio(args.youtube, temp_audio)
            temp_files.append(audio_path)
        else:
            parser.error("必须提供--audio或--youtube参数")
        
        # 执行转录
        result = transcribe_audio(
            audio_path, 
            model_name=args.model, 
            language=args.language, 
            task=args.task, 
            return_timestamps=args.timestamps
        )
        
        # 保存结果
        transcript = save_transcript(result, args.output)
        
        # 打印转录结果
        print("\n转录结果:")
        print(transcript)
        
    except Exception as e:
        logger.error(f"发生错误: {e}")
        raise
    finally:
        # 清理临时文件
        cleanup_temp_files(temp_files)

if __name__ == "__main__":
    main()
